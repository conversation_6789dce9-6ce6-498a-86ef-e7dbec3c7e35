# Git
.git
.gitignore

# Documentation
README.md
*.md

# Node modules
node_modules
npm-debug.log*

# Build artifacts
full-profile/output
full-profile/app_link

# Corpus data (can be large)
full-profile/corpus

# OS files
.DS_Store
Thumbs.db

# IDE files
.vscode
.idea
*.swp
*.swo

# Logs
*.log
logs

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Docker
Dockerfile*
docker-compose*
.dockerignore
