# CatalanPro - Aprèn <PERSON>

A comprehensive offline-first Progressive Web App (PWA) for learning Catalan using spaced repetition and intelligent flashcards.

## 🌟 Features

### Core Learning Features
- **Spaced Repetition System (SRS)**: FSRS-inspired algorithm for optimal review scheduling
- **Multiple Card Types**: Terms, sentences, conjugations, listening exercises, and cloze deletion
- **CEFR Level Organization**: Content organized by European language proficiency levels (A1-C2)
- **Audio Support**: Pronunciation audio with normal and slow speeds
- **IPA Transcriptions**: Phonetic transcriptions for accurate pronunciation
- **Multi-language Support**: Translations in Spanish, English, and Russian

### Technical Features
- **Offline-First**: Works completely offline after initial data load
- **Progressive Web App**: Installable on mobile and desktop
- **Streaming Import**: Efficient handling of large datasets with resume capability
- **Real-time Analytics**: Progress tracking and performance statistics
- **Card Editor**: Create and edit custom cards
- **Data Export/Import**: Backup and restore functionality

### User Experience
- **Responsive Design**: Optimized for mobile, tablet, and desktop
- **Keyboard Shortcuts**: Efficient navigation for power users
- **Multiple Review Modes**: Standard, reverse, listening, dictation, and speed modes
- **Dark/Light Theme**: Automatic or manual theme selection
- **Customizable Settings**: Personalize learning experience

## 🚀 Quick Start

### Option 1: Docker (Recommended)
```bash
# Clone the repository
git clone <repository-url>
cd catalanpro

# Start with Docker (easiest)
make start
# OR
./docker-start.sh start

# Open http://localhost:8080
```

### Option 2: Local Development
```bash
# Clone the repository
git clone <repository-url>
cd catalanpro

# Quick demo with included data
./demo.sh

# OR manual start
cd app
python3 -m http.server 8080
```

### Option 3: Build Custom Dataset
```bash
# Install dependencies
cd full-profile
npm install

# Build with lite profile (demo data)
node build_decks.js --profile=lite --output=../app/data

# Or build with full profile (requires corpus data)
node build_decks.js --profile=full --output=../app/data --verbose

# Serve the app
cd ../app
python3 -m http.server 8080
```

## 🐳 Docker Commands

### Production Mode
```bash
make start              # Start production app
make logs               # View logs
make stop               # Stop containers
make clean              # Clean up resources
```

### Development Mode
```bash
make dev                # Start with hot reload on :8081
make build              # Build dataset and start app
make health             # Check app health
make open               # Open in browser
```

### Using Docker Compose Directly
```bash
# Production
docker-compose up --build -d

# Development with hot reload
docker-compose --profile dev up --build

# Build dataset
docker-compose --profile build run --rm catalanpro-builder
```

## 📁 Project Structure

```
catalanpro/
├── app/                          # Main PWA application
│   ├── index.html               # Main HTML file
│   ├── manifest.webmanifest     # PWA manifest
│   ├── sw.js                    # Service worker
│   ├── css/
│   │   └── styles.css           # Application styles
│   ├── js/
│   │   ├── main.js              # Application bootstrap
│   │   ├── db.js                # Database layer (Dexie)
│   │   ├── srs.js               # Spaced repetition system
│   │   ├── importer.js          # Data import with streaming
│   │   ├── ui_dashboard.js      # Dashboard interface
│   │   ├── ui_review.js         # Review interface
│   │   ├── ui_editor.js         # Card editor
│   │   ├── ui_analytics.js      # Analytics dashboard
│   │   └── ui_settings.js       # Settings interface
│   └── data/                    # Learning data (JSONL format)
│       ├── decks_topics.jsonl   # Deck definitions
│       ├── media_manifest.jsonl # Audio file manifest
│       ├── cards_terms_A1.jsonl # A1 vocabulary cards
│       ├── cards_terms_A2.jsonl # A2 vocabulary cards
│       ├── cards_sentences_A1.jsonl # A1 sentence cards
│       └── ...                  # Additional card files
├── full-profile/                # Data pipeline for building datasets
│   ├── build_decks.js          # Main build orchestrator
│   ├── scripts/                # Pipeline scripts
│   │   ├── ingest.js           # Corpus ingestion
│   │   ├── normalize.js        # Data normalization
│   │   ├── analyze_freq_cefr.js # Frequency and CEFR analysis
│   │   ├── detect_mwe.js       # Multi-word expression detection
│   │   ├── generate_cards.js   # Card generation
│   │   ├── attach_audio_ipa.js # Audio and IPA processing
│   │   ├── validate.js         # Data validation
│   │   └── export_decks.js     # Final export
│   ├── corpus/                 # Source corpus data (not included)
│   └── output/                 # Build output directory
└── README.md                   # This file
```

## 🎯 Learning Methodology

### Spaced Repetition System
CatalanPro uses an FSRS-inspired algorithm that:
- Tracks card difficulty and stability over time
- Optimizes review intervals based on performance
- Adapts to individual learning patterns
- Minimizes review time while maximizing retention

### Card Types
1. **Term Cards**: Vocabulary with translations and examples
2. **Sentence Cards**: Complete sentences for context learning
3. **Conjugation Cards**: Verb forms and tenses
4. **Listening Cards**: Audio-based comprehension
5. **Cloze Cards**: Fill-in-the-blank exercises

### CEFR Integration
Content is organized by Common European Framework levels:
- **A1-A2**: Basic user (beginner to elementary)
- **B1-B2**: Independent user (intermediate to upper-intermediate)
- **C1-C2**: Proficient user (advanced to mastery)

### Review Modes
- **Standard**: CA → ES/EN/RU
- **Reverse**: ES/EN/RU → CA
- **Listening**: Audio → meaning
- **Dictation**: Audio → typing (tolerant)
- **Cloze**: Fill in the blanks
- **Speed**: Rapid recognition

### Keyboard Shortcuts
- `1/2/3/4`: Again/Hard/Good/Easy
- `Space`: Show/hide answer
- `A/S`: Audio speed control
- `Enter`: Next card

### Data Management
- **Import Wizard**: First-run setup with progress tracking
- **Resume**: Checkpoint-based import recovery
- **Backup/Restore**: Export/import user data
- **Analytics**: Progress tracking and statistics

## Builder Pipeline

### Scripts Overview
- `build_decks.js`: Main orchestrator
- `ingest.js`: Download/read corpora
- `normalize.js`: Clean and deduplicate
- `analyze_freq_cefr.js`: Frequency analysis and CEFR classification
- `detect_mwe.js`: Multi-word expression detection
- `generate_cards.js`: Card generation for all types
- `attach_audio_ipa.js`: IPA and audio manifest generation
- `validate.js`: Quality assurance
- `export_decks.js`: Final JSONL export

### Card Types Generated
- **Terms**: Vocabulary with translations, IPA, examples
- **Sentences**: Graded sentences with cloze variants
- **Conjugations**: Verb forms with examples
- **Listening**: Audio-based cards
- **Minimal Pairs**: Pronunciation practice

### Coverage Targets
- **Vocabulary**: 15-20k lemmas + common MWEs
- **Sentences**: 50-100k graded by CEFR level
- **Conjugations**: ~2,000 verbs, common tenses
- **Topics**: Daily life, work, travel, Catalonia-specific

## Technical Details

### Database Schema
- **Cards**: Core learning content with SRS metadata
- **ReviewLog**: Detailed review history
- **Decks**: Organizational structure
- **Media**: Audio file manifest
- **Settings**: User preferences and import state

### SRS Algorithm
- FSRS-inspired scheduling
- Stability and difficulty tracking
- Leech detection and handling
- Relearning steps

### Offline Support
- Service worker caching
- No runtime network calls
- Preloaded database
- PWA installability

## Development

### File Structure
```
app/
├── index.html
├── css/styles.css
├── js/
│   ├── db.js
│   ├── importer.js
│   ├── srs.js
│   ├── ui_*.js
│   └── main.js
├── data/ (generated)
├── media/ (generated)
├── manifest.webmanifest
└── sw.js

full-profile/
├── build_decks.js
├── scripts/
├── corpus/ (user downloads)
├── output/ (intermediate)
└── app_link/ (symlink target)
```

### Testing
```bash
# Test lite build
cd full-profile
node build_decks.js --profile=lite --test

# Validate full build
node build_decks.js --profile=full --validate
```

## License Notes

- Uses only open datasets (Wikipedia, Tatoeba, OPUS)
- No proprietary APIs or closed datasets
- Fully local processing
- Respects corpus licenses (CC, GPL where applicable)

## Troubleshooting

### Import Issues
- Check browser console for errors
- Verify JSONL file integrity
- Clear IndexedDB and retry import

### Audio Issues
- Ensure media files match manifest
- Check file paths and permissions
- Verify audio format support

### Performance
- Large datasets may take time to import
- Consider using --profile=standard for medium size
- Monitor memory usage during import

## 🔧 Technical Architecture

### Frontend
- **Vanilla JavaScript**: No framework dependencies for maximum performance
- **IndexedDB**: Client-side database via Dexie.js
- **Service Worker**: Offline functionality and caching
- **CSS Grid/Flexbox**: Responsive layout system

### Data Format
- **JSONL**: Streaming-friendly JSON Lines format
- **Modular**: Separate files by content type and level
- **Versioned**: Schema versioning for data migration

### Performance
- **Lazy Loading**: Cards loaded on demand
- **Streaming Import**: Large datasets imported incrementally
- **Efficient Queries**: Indexed database queries
- **Minimal Dependencies**: Only essential libraries

## 📱 PWA Features

### Installation
- Add to home screen on mobile devices
- Desktop installation via browser
- Offline functionality after first load
- Background sync for data updates

### Caching Strategy
- **Static Assets**: Cache-first with background updates
- **Data Files**: Stale-while-revalidate for freshness
- **API Calls**: Network-first with offline fallback

## 🎨 Customization

### Themes
- Light and dark themes
- Automatic system preference detection
- Custom CSS variables for easy theming

### Settings
- Daily study limits
- Review mode preferences
- Audio speed settings
- Translation language selection

### Card Editor
- Create custom vocabulary cards
- Add personal notes and examples
- Import/export custom decks
- Bulk editing capabilities

## 📊 Analytics

### Progress Tracking
- Daily study streaks
- Accuracy rates over time
- Cards learned by level
- Time spent studying

### Performance Metrics
- Review success rates
- Average response times
- Difficulty progression
- Retention curves

## 🌐 Internationalization

### UI Languages
- Catalan (primary)
- Spanish
- English
- Extensible for additional languages

### Content Languages
- Catalan (learning target)
- Spanish translations
- English translations
- Russian translations

## 🤝 Contributing

### Development Setup
```bash
# Clone repository
git clone <repository-url>
cd catalanpro

# Install build dependencies
cd full-profile
npm install

# Build demo data
node build_decks.js --profile=lite

# Start development server
cd ../app
python -m http.server 8080
```

### Adding Content
1. Place corpus files in `full-profile/corpus/`
2. Run build pipeline with appropriate profile
3. Test generated content in app
4. Submit pull request with changes

### Code Style
- ES6+ JavaScript
- Semantic HTML5
- Modern CSS (Grid, Flexbox, Custom Properties)
- Progressive enhancement principles

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

### License Notes
- Uses only open datasets (Wikipedia, Tatoeba, OPUS)
- No proprietary APIs or closed datasets
- Fully offline after initial data load
- No telemetry or tracking
- MIT license for code
- Corpus data follows original licenses

## 🙏 Acknowledgments

- FSRS algorithm inspiration from Anki and SuperMemo research
- Catalan linguistic resources from various open corpora
- PWA best practices from Google's web.dev
- Offline-first principles from the Service Worker community

## 📞 Support

For questions, issues, or contributions:
- Open an issue on GitHub
- Check the documentation in the `/docs` folder
- Review the build logs for troubleshooting

---

**CatalanPro** - Making Catalan accessible through intelligent, offline-first learning technology.
