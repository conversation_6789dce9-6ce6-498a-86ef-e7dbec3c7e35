# CatalanPro - Aprèn <PERSON>

A comprehensive offline-first Progressive Web App (PWA) for learning Catalan using spaced repetition and intelligent flashcards.

## 🌟 Features

### Core Learning Features
- **Spaced Repetition System (SRS)**: FSRS-inspired algorithm for optimal review scheduling
- **Multiple Card Types**: Terms, sentences, conjugations, listening exercises, and cloze deletion
- **CEFR Level Organization**: Content organized by European language proficiency levels (A1-C2)
- **Audio Support**: Pronunciation audio with normal and slow speeds
- **IPA Transcriptions**: Phonetic transcriptions for accurate pronunciation
- **Multi-language Support**: Translations in Spanish, English, and Russian

### Technical Features
- **Offline-First**: Works completely offline after initial data load
- **Progressive Web App**: Installable on mobile and desktop
- **Streaming Import**: Efficient handling of large datasets with resume capability
- **Real-time Analytics**: Progress tracking and performance statistics
- **Card Editor**: Create and edit custom cards
- **Data Export/Import**: Backup and restore functionality

### User Experience
- **Responsive Design**: Optimized for mobile, tablet, and desktop
- **Keyboard Shortcuts**: Efficient navigation for power users
- **Multiple Review Modes**: Standard, reverse, listening, dictation, and speed modes
- **Dark/Light Theme**: Automatic or manual theme selection
- **Customizable Settings**: Personalize learning experience

## 🚀 Quick Start

### Option 1: Use Pre-built Demo Data
```bash
# Clone the repository
git clone <repository-url>
cd catalanpro

# Serve the app (demo data included)
cd app
python -m http.server 8080

# Open in browser
open http://localhost:8080
```

### Option 2: Build Custom Dataset
```bash
# Install dependencies
cd full-profile
npm install

# Build with lite profile (demo data)
node build_decks.js --profile=lite --output=../app/data

# Or build with full profile (requires corpus data)
node build_decks.js --profile=full --output=../app/data --verbose

# Serve the app
cd ../app
python -m http.server 8080
```

## 📁 Project Structure

```
catalanpro/
├── app/                          # Main PWA application
│   ├── index.html               # Main HTML file
│   ├── manifest.webmanifest     # PWA manifest
│   ├── sw.js                    # Service worker
│   ├── css/
│   │   └── styles.css           # Application styles
│   ├── js/
│   │   ├── main.js              # Application bootstrap
│   │   ├── db.js                # Database layer (Dexie)
│   │   ├── srs.js               # Spaced repetition system
│   │   ├── importer.js          # Data import with streaming
│   │   ├── ui_dashboard.js      # Dashboard interface
│   │   ├── ui_review.js         # Review interface
│   │   ├── ui_editor.js         # Card editor
│   │   ├── ui_analytics.js      # Analytics dashboard
│   │   └── ui_settings.js       # Settings interface
│   └── data/                    # Learning data (JSONL format)
│       ├── decks_topics.jsonl   # Deck definitions
│       ├── media_manifest.jsonl # Audio file manifest
│       ├── cards_terms_A1.jsonl # A1 vocabulary cards
│       ├── cards_terms_A2.jsonl # A2 vocabulary cards
│       ├── cards_sentences_A1.jsonl # A1 sentence cards
│       └── ...                  # Additional card files
├── full-profile/                # Data pipeline for building datasets
│   ├── build_decks.js          # Main build orchestrator
│   ├── scripts/                # Pipeline scripts
│   │   ├── ingest.js           # Corpus ingestion
│   │   ├── normalize.js        # Data normalization
│   │   ├── analyze_freq_cefr.js # Frequency and CEFR analysis
│   │   ├── detect_mwe.js       # Multi-word expression detection
│   │   ├── generate_cards.js   # Card generation
│   │   ├── attach_audio_ipa.js # Audio and IPA processing
│   │   ├── validate.js         # Data validation
│   │   └── export_decks.js     # Final export
│   ├── corpus/                 # Source corpus data (not included)
│   └── output/                 # Build output directory
└── README.md                   # This file
```

## App Features

### Review Modes
- **Standard**: CA → ES/EN/RU
- **Reverse**: ES/EN/RU → CA
- **Listening**: Audio → meaning
- **Dictation**: Audio → typing (tolerant)
- **Cloze**: Fill in the blanks
- **Speed**: Rapid recognition

### Keyboard Shortcuts
- `1/2/3/4`: Again/Hard/Good/Easy
- `Space`: Show/hide answer
- `A/S`: Audio speed control
- `Enter`: Next card

### Data Management
- **Import Wizard**: First-run setup with progress tracking
- **Resume**: Checkpoint-based import recovery
- **Backup/Restore**: Export/import user data
- **Analytics**: Progress tracking and statistics

## Builder Pipeline

### Scripts Overview
- `build_decks.js`: Main orchestrator
- `ingest.js`: Download/read corpora
- `normalize.js`: Clean and deduplicate
- `analyze_freq_cefr.js`: Frequency analysis and CEFR classification
- `detect_mwe.js`: Multi-word expression detection
- `generate_cards.js`: Card generation for all types
- `attach_audio_ipa.js`: IPA and audio manifest generation
- `validate.js`: Quality assurance
- `export_decks.js`: Final JSONL export

### Card Types Generated
- **Terms**: Vocabulary with translations, IPA, examples
- **Sentences**: Graded sentences with cloze variants
- **Conjugations**: Verb forms with examples
- **Listening**: Audio-based cards
- **Minimal Pairs**: Pronunciation practice

### Coverage Targets
- **Vocabulary**: 15-20k lemmas + common MWEs
- **Sentences**: 50-100k graded by CEFR level
- **Conjugations**: ~2,000 verbs, common tenses
- **Topics**: Daily life, work, travel, Catalonia-specific

## Technical Details

### Database Schema
- **Cards**: Core learning content with SRS metadata
- **ReviewLog**: Detailed review history
- **Decks**: Organizational structure
- **Media**: Audio file manifest
- **Settings**: User preferences and import state

### SRS Algorithm
- FSRS-inspired scheduling
- Stability and difficulty tracking
- Leech detection and handling
- Relearning steps

### Offline Support
- Service worker caching
- No runtime network calls
- Preloaded database
- PWA installability

## Development

### File Structure
```
app/
├── index.html
├── css/styles.css
├── js/
│   ├── db.js
│   ├── importer.js
│   ├── srs.js
│   ├── ui_*.js
│   └── main.js
├── data/ (generated)
├── media/ (generated)
├── manifest.webmanifest
└── sw.js

full-profile/
├── build_decks.js
├── scripts/
├── corpus/ (user downloads)
├── output/ (intermediate)
└── app_link/ (symlink target)
```

### Testing
```bash
# Test lite build
cd full-profile
node build_decks.js --profile=lite --test

# Validate full build
node build_decks.js --profile=full --validate
```

## License Notes

- Uses only open datasets (Wikipedia, Tatoeba, OPUS)
- No proprietary APIs or closed datasets
- Fully local processing
- Respects corpus licenses (CC, GPL where applicable)

## Troubleshooting

### Import Issues
- Check browser console for errors
- Verify JSONL file integrity
- Clear IndexedDB and retry import

### Audio Issues
- Ensure media files match manifest
- Check file paths and permissions
- Verify audio format support

### Performance
- Large datasets may take time to import
- Consider using --profile=standard for medium size
- Monitor memory usage during import
