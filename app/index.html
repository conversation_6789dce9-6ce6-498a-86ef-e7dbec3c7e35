<!DOCTYPE html>
<html lang="ca">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CatalanPro - Aprèn Català</title>
    <meta name="description" content="Aprèn català amb targetes intel·ligents offline">
    <meta name="theme-color" content="#d32f2f">
    
    <!-- PWA -->
    <link rel="manifest" href="manifest.webmanifest">
    <link rel="icon" type="image/png" sizes="192x192" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏛️</text></svg>">
    
    <!-- Styles -->
    <link rel="stylesheet" href="css/styles.css">
    
    <!-- Dexie -->
    <script src="https://unpkg.com/dexie@3.2.4/dist/dexie.js"></script>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="logo">🏛️</div>
            <h1>CatalanPro</h1>
            <div class="loading-spinner"></div>
            <p id="loading-text">Carregant...</p>
        </div>
    </div>

    <!-- Import Wizard Modal -->
    <div id="import-wizard" class="modal hidden">
        <div class="modal-content">
            <h2>🚀 Configuració inicial</h2>
            <p>Importem les dades d'aprenentatge. Això pot trigar uns minuts.</p>
            
            <div class="import-progress">
                <div class="file-progress">
                    <div class="file-name" id="current-file">Preparant...</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="file-progress"></div>
                    </div>
                    <div class="progress-text" id="file-progress-text">0%</div>
                </div>
                
                <div class="overall-progress">
                    <div class="progress-label">Progrés total</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="overall-progress"></div>
                    </div>
                    <div class="progress-text" id="overall-progress-text">0%</div>
                </div>
            </div>
            
            <div class="import-log" id="import-log"></div>
            
            <div class="import-actions">
                <button id="pause-import" class="btn btn-secondary">Pausa</button>
                <button id="resume-import" class="btn btn-primary hidden">Continua</button>
                <button id="skip-import" class="btn btn-outline">Ometre (mode demo)</button>
            </div>
        </div>
    </div>

    <!-- Main App -->
    <div id="app" class="app hidden">
        <!-- Navigation -->
        <nav class="navbar">
            <div class="nav-brand">
                <span class="logo">🏛️</span>
                <span class="brand-text">CatalanPro</span>
            </div>
            <div class="nav-menu">
                <button class="nav-item active" data-view="dashboard">
                    <span class="nav-icon">📊</span>
                    <span class="nav-text">Inici</span>
                </button>
                <button class="nav-item" data-view="review">
                    <span class="nav-icon">🎯</span>
                    <span class="nav-text">Repassar</span>
                </button>
                <button class="nav-item" data-view="editor">
                    <span class="nav-icon">✏️</span>
                    <span class="nav-text">Editor</span>
                </button>
                <button class="nav-item" data-view="analytics">
                    <span class="nav-icon">📈</span>
                    <span class="nav-text">Estadístiques</span>
                </button>
                <button class="nav-item" data-view="settings">
                    <span class="nav-icon">⚙️</span>
                    <span class="nav-text">Configuració</span>
                </button>
            </div>
        </nav>

        <!-- Views -->
        <main class="main-content">
            <!-- Dashboard View -->
            <div id="dashboard-view" class="view active">
                <div class="dashboard-header">
                    <h1>Bon dia! 👋</h1>
                    <p class="dashboard-subtitle">Continua el teu aprenentatge del català</p>
                </div>
                
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-icon">🎯</div>
                        <div class="stat-content">
                            <div class="stat-number" id="due-count">0</div>
                            <div class="stat-label">Per repassar</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🆕</div>
                        <div class="stat-content">
                            <div class="stat-number" id="new-count">0</div>
                            <div class="stat-label">Noves</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🔥</div>
                        <div class="stat-content">
                            <div class="stat-number" id="streak-count">0</div>
                            <div class="stat-label">Dies seguits</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📚</div>
                        <div class="stat-content">
                            <div class="stat-number" id="total-cards">0</div>
                            <div class="stat-label">Total targetes</div>
                        </div>
                    </div>
                </div>

                <div class="dashboard-actions">
                    <button id="start-review" class="btn btn-primary btn-large">
                        <span class="btn-icon">🎯</span>
                        Començar repàs
                    </button>
                    <button id="study-new" class="btn btn-secondary btn-large">
                        <span class="btn-icon">🆕</span>
                        Estudiar noves
                    </button>
                </div>

                <div class="dashboard-decks">
                    <h2>Mazos disponibles</h2>
                    <div id="deck-list" class="deck-grid">
                        <!-- Populated by JS -->
                    </div>
                </div>
            </div>

            <!-- Review View -->
            <div id="review-view" class="view">
                <!-- Populated by ui_review.js -->
            </div>

            <!-- Editor View -->
            <div id="editor-view" class="view">
                <!-- Populated by ui_editor.js -->
            </div>

            <!-- Analytics View -->
            <div id="analytics-view" class="view">
                <!-- Populated by ui_analytics.js -->
            </div>

            <!-- Settings View -->
            <div id="settings-view" class="view">
                <!-- Populated by ui_settings.js -->
            </div>
        </main>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Scripts -->
    <script src="js/db.js"></script>
    <script src="js/srs.js"></script>
    <script src="js/importer.js"></script>
    <script src="js/ui_dashboard.js"></script>
    <script src="js/ui_review.js"></script>
    <script src="js/ui_editor.js"></script>
    <script src="js/ui_analytics.js"></script>
    <script src="js/ui_settings.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
