// Editor UI Module
// Handles card creation and editing

class EditorUI {
    constructor() {
        this.currentCard = null;
        this.isEditing = false;
        this.availableDecks = [];
    }

    /**
     * Initialize the editor UI
     */
    async init() {
        console.log('Initializing Editor UI');
        await this.loadDecks();
        this.setupEventHandlers();
    }

    /**
     * Show the editor view
     */
    async show() {
        await this.renderEditor();
        await this.loadDecks();
    }

    /**
     * Load available decks
     */
    async loadDecks() {
        try {
            this.availableDecks = await db.decks.toArray();
        } catch (error) {
            console.error('Error loading decks:', error);
        }
    }

    /**
     * Set up event handlers
     */
    setupEventHandlers() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('#save-card')) {
                this.saveCard();
            }
            if (e.target.matches('#new-card')) {
                this.newCard();
            }
            if (e.target.matches('#delete-card')) {
                this.deleteCard();
            }
            if (e.target.matches('.card-search-result')) {
                const cardId = e.target.dataset.cardId;
                this.loadCard(cardId);
            }
        });

        document.addEventListener('input', (e) => {
            if (e.target.matches('#card-search')) {
                this.searchCards(e.target.value);
            }
        });
    }

    /**
     * Render the editor interface
     */
    async renderEditor() {
        const editorView = document.getElementById('editor-view');
        if (!editorView) return;

        editorView.innerHTML = `
            <div class="editor-container">
                <div class="editor-sidebar">
                    <h2>Editor de Tarjetas</h2>
                    
                    <div class="card-search">
                        <input type="text" id="card-search" placeholder="Buscar tarjetas..." class="search-input">
                        <div id="search-results" class="search-results"></div>
                    </div>
                    
                    <div class="editor-actions">
                        <button id="new-card" class="btn btn-primary">
                            <span class="btn-icon">➕</span>
                            Nueva Tarjeta
                        </button>
                    </div>
                </div>

                <div class="editor-main">
                    <div id="card-form" class="card-form">
                        <div class="form-group">
                            <label for="card-type">Tipo de Tarjeta</label>
                            <select id="card-type" class="form-control">
                                <option value="term">Término</option>
                                <option value="sentence">Frase</option>
                                <option value="conjugation">Conjugación</option>
                                <option value="listening">Escucha</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="card-catalan">Catalán *</label>
                            <input type="text" id="card-catalan" class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label for="card-ipa">IPA</label>
                            <input type="text" id="card-ipa" class="form-control" placeholder="[pronunciación]">
                        </div>

                        <div class="translations-group">
                            <h3>Traducciones</h3>
                            <div class="form-group">
                                <label for="translation-es">Español</label>
                                <input type="text" id="translation-es" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="translation-en">Inglés</label>
                                <input type="text" id="translation-en" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="translation-ru">Ruso</label>
                                <input type="text" id="translation-ru" class="form-control">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="card-examples">Ejemplos (uno por línea)</label>
                            <textarea id="card-examples" class="form-control" rows="3"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="card-grammar">Consejos Gramaticales (uno por línea)</label>
                            <textarea id="card-grammar" class="form-control" rows="2"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="card-tags">Etiquetas (separadas por comas)</label>
                            <input type="text" id="card-tags" class="form-control" placeholder="básico, verbo, cotidiano">
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="card-cefr">Nivel CEFR</label>
                                <select id="card-cefr" class="form-control">
                                    <option value="A1">A1</option>
                                    <option value="A2">A2</option>
                                    <option value="B1">B1</option>
                                    <option value="B2">B2</option>
                                    <option value="C1">C1</option>
                                    <option value="C2">C2</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="card-deck">Mazo</label>
                                <select id="card-deck" class="form-control">
                                    ${this.renderDeckOptions()}
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="card-notes">Notas Personales</label>
                            <textarea id="card-notes" class="form-control" rows="2"></textarea>
                        </div>

                        <div class="form-actions">
                            <button id="save-card" class="btn btn-primary">
                                <span class="btn-icon">💾</span>
                                Guardar
                            </button>
                            <button id="delete-card" class="btn btn-outline hidden">
                                <span class="btn-icon">🗑️</span>
                                Eliminar
                            </button>
                        </div>
                    </div>

                    <div id="card-preview" class="card-preview">
                        <h3>Vista Previa</h3>
                        <div id="preview-content" class="preview-content">
                            <p>Completa los campos para ver la vista previa</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.setupFormValidation();
        this.setupPreview();
    }

    /**
     * Render deck options for select
     */
    renderDeckOptions() {
        return this.availableDecks.map(deck => 
            `<option value="${deck.id}">${this.escapeHtml(deck.title)}</option>`
        ).join('');
    }

    /**
     * Set up form validation
     */
    setupFormValidation() {
        const form = document.getElementById('card-form');
        if (!form) return;

        form.addEventListener('input', (e) => {
            this.validateForm();
            this.updatePreview();
        });

        form.addEventListener('change', (e) => {
            this.validateForm();
            this.updatePreview();
        });
    }

    /**
     * Set up live preview
     */
    setupPreview() {
        this.updatePreview();
    }

    /**
     * Update card preview
     */
    updatePreview() {
        const previewContent = document.getElementById('preview-content');
        if (!previewContent) return;

        const formData = this.getFormData();
        
        if (!formData.catalan) {
            previewContent.innerHTML = '<p>Completa los campos para ver la vista previa</p>';
            return;
        }

        const translations = Object.entries(formData.translations)
            .filter(([lang, text]) => text)
            .map(([lang, text]) => `
                <div class="preview-translation">
                    <span class="lang-code">${lang.toUpperCase()}</span>
                    <span class="translation-text">${this.escapeHtml(text)}</span>
                </div>
            `).join('');

        const examples = formData.examples.length > 0 ? `
            <div class="preview-examples">
                <strong>Ejemplos:</strong>
                ${formData.examples.map(ex => `<div>${this.escapeHtml(ex)}</div>`).join('')}
            </div>
        ` : '';

        previewContent.innerHTML = `
            <div class="preview-card">
                <div class="preview-question">
                    <div class="card-text-main">${this.escapeHtml(formData.catalan)}</div>
                    ${formData.ipa ? `<div class="card-ipa">[${this.escapeHtml(formData.ipa)}]</div>` : ''}
                </div>
                <div class="preview-answer">
                    ${translations}
                    ${examples}
                </div>
                <div class="preview-meta">
                    <span class="meta-item">Tipo: ${formData.type}</span>
                    <span class="meta-item">CEFR: ${formData.cefr}</span>
                    ${formData.tags.length > 0 ? `<span class="meta-item">Etiquetas: ${formData.tags.join(', ')}</span>` : ''}
                </div>
            </div>
        `;
    }

    /**
     * Get form data
     */
    getFormData() {
        return {
            type: document.getElementById('card-type')?.value || 'term',
            catalan: document.getElementById('card-catalan')?.value || '',
            ipa: document.getElementById('card-ipa')?.value || '',
            translations: {
                es: document.getElementById('translation-es')?.value || '',
                en: document.getElementById('translation-en')?.value || '',
                ru: document.getElementById('translation-ru')?.value || ''
            },
            examples: this.parseTextareaLines('card-examples'),
            grammarTips: this.parseTextareaLines('card-grammar'),
            tags: this.parseTags('card-tags'),
            cefr: document.getElementById('card-cefr')?.value || 'A1',
            deckId: document.getElementById('card-deck')?.value || '',
            notes: document.getElementById('card-notes')?.value || ''
        };
    }

    /**
     * Parse textarea lines
     */
    parseTextareaLines(elementId) {
        const element = document.getElementById(elementId);
        if (!element) return [];
        
        return element.value
            .split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);
    }

    /**
     * Parse tags from comma-separated string
     */
    parseTags(elementId) {
        const element = document.getElementById(elementId);
        if (!element) return [];
        
        return element.value
            .split(',')
            .map(tag => tag.trim())
            .filter(tag => tag.length > 0);
    }

    /**
     * Validate form
     */
    validateForm() {
        const saveBtn = document.getElementById('save-card');
        const catalan = document.getElementById('card-catalan')?.value;
        const deckId = document.getElementById('card-deck')?.value;
        
        const isValid = catalan && catalan.trim().length > 0 && deckId;
        
        if (saveBtn) {
            saveBtn.disabled = !isValid;
        }
        
        return isValid;
    }

    /**
     * Create new card
     */
    newCard() {
        this.currentCard = null;
        this.isEditing = false;
        this.clearForm();
        
        const deleteBtn = document.getElementById('delete-card');
        if (deleteBtn) deleteBtn.classList.add('hidden');
    }

    /**
     * Load card for editing
     */
    async loadCard(cardId) {
        try {
            this.currentCard = await db.cards.get(cardId);
            if (!this.currentCard) {
                window.app.showToast('Tarjeta no encontrada', 'error');
                return;
            }
            
            this.isEditing = true;
            this.populateForm(this.currentCard);
            
            const deleteBtn = document.getElementById('delete-card');
            if (deleteBtn) deleteBtn.classList.remove('hidden');
            
        } catch (error) {
            console.error('Error loading card:', error);
            window.app.showToast('Error al cargar la tarjeta', 'error');
        }
    }

    /**
     * Populate form with card data
     */
    populateForm(card) {
        document.getElementById('card-type').value = card.type || 'term';
        document.getElementById('card-catalan').value = card.catalan || '';
        document.getElementById('card-ipa').value = card.ipa || '';
        document.getElementById('translation-es').value = card.translations?.es || '';
        document.getElementById('translation-en').value = card.translations?.en || '';
        document.getElementById('translation-ru').value = card.translations?.ru || '';
        document.getElementById('card-examples').value = (card.examples || []).join('\n');
        document.getElementById('card-grammar').value = (card.grammarTips || []).join('\n');
        document.getElementById('card-tags').value = (card.tags || []).join(', ');
        document.getElementById('card-cefr').value = card.cefr || 'A1';
        document.getElementById('card-deck').value = card.deckId || '';
        document.getElementById('card-notes').value = card.notes || '';
        
        this.updatePreview();
    }

    /**
     * Clear form
     */
    clearForm() {
        const form = document.getElementById('card-form');
        if (form) {
            form.reset();
            this.updatePreview();
        }
    }

    /**
     * Save card
     */
    async saveCard() {
        if (!this.validateForm()) {
            window.app.showToast('Por favor completa los campos requeridos', 'warning');
            return;
        }

        try {
            const formData = this.getFormData();
            const now = Date.now();
            
            const cardData = {
                ...formData,
                syllables: this.generateSyllables(formData.catalan),
                audioIds: [], // TODO: Handle audio
                freqRank: 0, // TODO: Calculate frequency
                ease: 2.5,
                interval: 0,
                due: 0,
                stability: 4.0,
                difficulty: 4.0,
                lapses: 0,
                leechCount: 0,
                updatedAt: now
            };

            if (this.isEditing && this.currentCard) {
                // Update existing card
                await db.cards.update(this.currentCard.id, cardData);
                window.app.showToast('Tarjeta actualizada', 'success');
            } else {
                // Create new card
                cardData.id = this.generateCardId();
                cardData.createdAt = now;
                await db.cards.add(cardData);
                window.app.showToast('Tarjeta creada', 'success');
                this.newCard(); // Reset form for next card
            }

        } catch (error) {
            console.error('Error saving card:', error);
            window.app.showToast('Error al guardar la tarjeta', 'error');
        }
    }

    /**
     * Delete card
     */
    async deleteCard() {
        if (!this.currentCard) return;

        if (!confirm('¿Estás seguro de que quieres eliminar esta tarjeta?')) {
            return;
        }

        try {
            await db.cards.delete(this.currentCard.id);
            window.app.showToast('Tarjeta eliminada', 'success');
            this.newCard();
        } catch (error) {
            console.error('Error deleting card:', error);
            window.app.showToast('Error al eliminar la tarjeta', 'error');
        }
    }

    /**
     * Search cards
     */
    async searchCards(query) {
        const resultsContainer = document.getElementById('search-results');
        if (!resultsContainer) return;

        if (query.length < 2) {
            resultsContainer.innerHTML = '';
            return;
        }

        try {
            const cards = await db.cards
                .filter(card => 
                    card.catalan.toLowerCase().includes(query.toLowerCase()) ||
                    Object.values(card.translations || {}).some(trans => 
                        trans.toLowerCase().includes(query.toLowerCase())
                    )
                )
                .limit(10)
                .toArray();

            resultsContainer.innerHTML = cards.map(card => `
                <div class="card-search-result" data-card-id="${card.id}">
                    <div class="result-main">${this.escapeHtml(card.catalan)}</div>
                    <div class="result-translation">${this.escapeHtml(card.translations?.es || '')}</div>
                </div>
            `).join('');

        } catch (error) {
            console.error('Error searching cards:', error);
        }
    }

    /**
     * Generate syllables (simple implementation)
     */
    generateSyllables(text) {
        // Very basic syllable splitting for Catalan
        // In a real implementation, this would use proper linguistic rules
        return text.split(/[aeiouàèéíòóú]/i).filter(s => s.length > 0);
    }

    /**
     * Generate unique card ID
     */
    generateCardId() {
        return 'card-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Escape HTML
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Register globally
window.EditorUI = EditorUI;
