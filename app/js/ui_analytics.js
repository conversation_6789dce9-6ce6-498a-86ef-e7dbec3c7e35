// Analytics UI Module
// Handles progress tracking and statistics display

class AnalyticsUI {
    constructor() {
        this.chartData = {};
        this.timeRange = '30d'; // '7d', '30d', '90d', 'all'
    }

    /**
     * Initialize the analytics UI
     */
    async init() {
        console.log('Initializing Analytics UI');
        this.setupEventHandlers();
    }

    /**
     * Show the analytics view
     */
    async show() {
        await this.renderAnalytics();
        await this.loadAnalyticsData();
    }

    /**
     * Set up event handlers
     */
    setupEventHandlers() {
        document.addEventListener('change', (e) => {
            if (e.target.matches('#time-range')) {
                this.timeRange = e.target.value;
                this.loadAnalyticsData();
            }
        });

        document.addEventListener('click', (e) => {
            if (e.target.matches('#export-data')) {
                this.exportData();
            }
            if (e.target.matches('#import-data')) {
                this.importData();
            }
        });
    }

    /**
     * Render the analytics interface
     */
    async renderAnalytics() {
        const analyticsView = document.getElementById('analytics-view');
        if (!analyticsView) return;

        analyticsView.innerHTML = `
            <div class="analytics-container">
                <div class="analytics-header">
                    <h1>Estadísticas de Progreso</h1>
                    <div class="analytics-controls">
                        <select id="time-range" class="form-control">
                            <option value="7d">Últimos 7 días</option>
                            <option value="30d" selected>Últimos 30 días</option>
                            <option value="90d">Últimos 90 días</option>
                            <option value="all">Todo el tiempo</option>
                        </select>
                    </div>
                </div>

                <div class="analytics-grid">
                    <!-- Overview Stats -->
                    <div class="analytics-card overview-stats">
                        <h2>Resumen General</h2>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value" id="total-reviews">0</div>
                                <div class="stat-label">Repasos totales</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="accuracy-rate">0%</div>
                                <div class="stat-label">Precisión</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="avg-time">0s</div>
                                <div class="stat-label">Tiempo promedio</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="streak-days">0</div>
                                <div class="stat-label">Días seguidos</div>
                            </div>
                        </div>
                    </div>

                    <!-- Daily Activity -->
                    <div class="analytics-card daily-activity">
                        <h2>Actividad Diaria</h2>
                        <div id="daily-chart" class="chart-container">
                            <div class="chart-placeholder">Cargando gráfico...</div>
                        </div>
                    </div>

                    <!-- Performance by CEFR Level -->
                    <div class="analytics-card cefr-performance">
                        <h2>Rendimiento por Nivel CEFR</h2>
                        <div id="cefr-chart" class="chart-container">
                            <div class="chart-placeholder">Cargando gráfico...</div>
                        </div>
                    </div>

                    <!-- Card Type Distribution -->
                    <div class="analytics-card card-types">
                        <h2>Distribución por Tipo</h2>
                        <div id="type-chart" class="chart-container">
                            <div class="chart-placeholder">Cargando gráfico...</div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="analytics-card recent-activity">
                        <h2>Actividad Reciente</h2>
                        <div id="recent-sessions" class="activity-list">
                            <!-- Populated by JS -->
                        </div>
                    </div>

                    <!-- Learning Progress -->
                    <div class="analytics-card learning-progress">
                        <h2>Progreso de Aprendizaje</h2>
                        <div class="progress-metrics">
                            <div class="progress-item">
                                <div class="progress-label">Tarjetas aprendidas</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="learned-progress"></div>
                                </div>
                                <div class="progress-text" id="learned-text">0 / 0</div>
                            </div>
                            <div class="progress-item">
                                <div class="progress-label">Nivel promedio</div>
                                <div class="level-indicator" id="avg-level">A1</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="analytics-actions">
                    <button id="export-data" class="btn btn-primary">
                        <span class="btn-icon">📤</span>
                        Exportar Datos
                    </button>
                    <button id="import-data" class="btn btn-secondary">
                        <span class="btn-icon">📥</span>
                        Importar Datos
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Load analytics data
     */
    async loadAnalyticsData() {
        try {
            const timeRangeMs = this.getTimeRangeMs();
            const cutoffDate = Date.now() - timeRangeMs;

            // Load review data
            const reviews = await db.reviewLog
                .where('timestamp')
                .above(cutoffDate)
                .toArray();

            // Load card data
            const cards = await db.cards.toArray();

            // Calculate statistics
            await this.calculateOverviewStats(reviews, cards);
            await this.generateDailyChart(reviews);
            await this.generateCEFRChart(reviews, cards);
            await this.generateTypeChart(cards);
            await this.loadRecentActivity(reviews);
            await this.calculateLearningProgress(cards);

        } catch (error) {
            console.error('Error loading analytics data:', error);
        }
    }

    /**
     * Calculate overview statistics
     */
    async calculateOverviewStats(reviews, cards) {
        const totalReviews = reviews.length;
        const correctReviews = reviews.filter(r => r.rating >= 3).length;
        const accuracyRate = totalReviews > 0 ? (correctReviews / totalReviews * 100) : 0;
        
        const avgTime = reviews.length > 0 
            ? reviews.reduce((sum, r) => sum + r.elapsedMs, 0) / reviews.length / 1000
            : 0;

        const streakDays = await this.calculateStreak();

        // Update display
        document.getElementById('total-reviews').textContent = totalReviews.toLocaleString();
        document.getElementById('accuracy-rate').textContent = `${accuracyRate.toFixed(1)}%`;
        document.getElementById('avg-time').textContent = `${avgTime.toFixed(1)}s`;
        document.getElementById('streak-days').textContent = streakDays;
    }

    /**
     * Generate daily activity chart
     */
    async generateDailyChart(reviews) {
        const chartContainer = document.getElementById('daily-chart');
        if (!chartContainer) return;

        // Group reviews by day
        const dailyData = {};
        const days = this.getTimeRange() === '7d' ? 7 : 30;
        
        // Initialize all days with 0
        for (let i = 0; i < days; i++) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dayKey = date.toISOString().split('T')[0];
            dailyData[dayKey] = 0;
        }

        // Count reviews per day
        reviews.forEach(review => {
            const dayKey = new Date(review.timestamp).toISOString().split('T')[0];
            if (dailyData.hasOwnProperty(dayKey)) {
                dailyData[dayKey]++;
            }
        });

        // Render simple bar chart
        const maxReviews = Math.max(...Object.values(dailyData), 1);
        const chartHTML = Object.entries(dailyData)
            .reverse()
            .map(([date, count]) => {
                const height = (count / maxReviews) * 100;
                const dayName = new Date(date).toLocaleDateString('ca', { weekday: 'short' });
                return `
                    <div class="chart-bar" style="height: ${height}%" title="${count} repasos el ${date}">
                        <div class="bar-value">${count}</div>
                        <div class="bar-label">${dayName}</div>
                    </div>
                `;
            }).join('');

        chartContainer.innerHTML = `<div class="simple-chart">${chartHTML}</div>`;
    }

    /**
     * Generate CEFR level performance chart
     */
    async generateCEFRChart(reviews, cards) {
        const chartContainer = document.getElementById('cefr-chart');
        if (!chartContainer) return;

        // Create card lookup
        const cardLookup = {};
        cards.forEach(card => {
            cardLookup[card.id] = card;
        });

        // Group by CEFR level
        const cefrData = {};
        reviews.forEach(review => {
            const card = cardLookup[review.cardId];
            if (card && card.cefr) {
                if (!cefrData[card.cefr]) {
                    cefrData[card.cefr] = { total: 0, correct: 0 };
                }
                cefrData[card.cefr].total++;
                if (review.rating >= 3) {
                    cefrData[card.cefr].correct++;
                }
            }
        });

        // Render CEFR chart
        const cefrLevels = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'];
        const chartHTML = cefrLevels.map(level => {
            const data = cefrData[level] || { total: 0, correct: 0 };
            const accuracy = data.total > 0 ? (data.correct / data.total * 100) : 0;
            return `
                <div class="cefr-item">
                    <div class="cefr-level">${level}</div>
                    <div class="cefr-bar">
                        <div class="cefr-fill" style="width: ${accuracy}%"></div>
                    </div>
                    <div class="cefr-text">${accuracy.toFixed(1)}% (${data.total})</div>
                </div>
            `;
        }).join('');

        chartContainer.innerHTML = chartHTML;
    }

    /**
     * Generate card type distribution chart
     */
    async generateTypeChart(cards) {
        const chartContainer = document.getElementById('type-chart');
        if (!chartContainer) return;

        // Count by type
        const typeData = {};
        cards.forEach(card => {
            typeData[card.type] = (typeData[card.type] || 0) + 1;
        });

        const total = cards.length;
        const chartHTML = Object.entries(typeData).map(([type, count]) => {
            const percentage = total > 0 ? (count / total * 100) : 0;
            const typeLabel = this.getTypeLabel(type);
            return `
                <div class="type-item">
                    <div class="type-label">${typeLabel}</div>
                    <div class="type-bar">
                        <div class="type-fill" style="width: ${percentage}%"></div>
                    </div>
                    <div class="type-text">${count} (${percentage.toFixed(1)}%)</div>
                </div>
            `;
        }).join('');

        chartContainer.innerHTML = chartHTML;
    }

    /**
     * Load recent activity
     */
    async loadRecentActivity(reviews) {
        const container = document.getElementById('recent-sessions');
        if (!container) return;

        // Group reviews by session (within 1 hour)
        const sessions = this.groupReviewsIntoSessions(reviews);
        const recentSessions = sessions.slice(0, 10);

        const sessionHTML = recentSessions.map(session => {
            const date = new Date(session.startTime).toLocaleDateString();
            const time = new Date(session.startTime).toLocaleTimeString();
            const accuracy = session.total > 0 ? (session.correct / session.total * 100) : 0;
            
            return `
                <div class="activity-item">
                    <div class="activity-time">${date} ${time}</div>
                    <div class="activity-stats">
                        ${session.total} tarjetas, ${accuracy.toFixed(1)}% precisión
                    </div>
                    <div class="activity-duration">${this.formatDuration(session.duration)}</div>
                </div>
            `;
        }).join('');

        container.innerHTML = sessionHTML || '<div class="empty-state">No hay actividad reciente</div>';
    }

    /**
     * Calculate learning progress
     */
    async calculateLearningProgress(cards) {
        const learnedCards = cards.filter(card => card.interval >= 1).length;
        const totalCards = cards.length;
        const learnedPercentage = totalCards > 0 ? (learnedCards / totalCards * 100) : 0;

        // Calculate average CEFR level
        const cefrValues = { A1: 1, A2: 2, B1: 3, B2: 4, C1: 5, C2: 6 };
        const avgCefrValue = cards.length > 0 
            ? cards.reduce((sum, card) => sum + (cefrValues[card.cefr] || 1), 0) / cards.length
            : 1;
        const avgLevel = Object.keys(cefrValues).find(level => cefrValues[level] === Math.round(avgCefrValue)) || 'A1';

        // Update display
        const progressFill = document.getElementById('learned-progress');
        const progressText = document.getElementById('learned-text');
        const avgLevelEl = document.getElementById('avg-level');

        if (progressFill) progressFill.style.width = `${learnedPercentage}%`;
        if (progressText) progressText.textContent = `${learnedCards} / ${totalCards}`;
        if (avgLevelEl) avgLevelEl.textContent = avgLevel;
    }

    /**
     * Helper methods
     */
    getTimeRangeMs() {
        switch (this.timeRange) {
            case '7d': return 7 * 24 * 60 * 60 * 1000;
            case '30d': return 30 * 24 * 60 * 60 * 1000;
            case '90d': return 90 * 24 * 60 * 60 * 1000;
            default: return Number.MAX_SAFE_INTEGER;
        }
    }

    getTimeRange() {
        return this.timeRange;
    }

    async calculateStreak() {
        // Simplified streak calculation
        const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
        const recentReviews = await db.reviewLog
            .where('timestamp')
            .above(thirtyDaysAgo)
            .toArray();

        const reviewsByDay = {};
        recentReviews.forEach(review => {
            const day = new Date(review.timestamp).toDateString();
            reviewsByDay[day] = true;
        });

        let streak = 0;
        const today = new Date();
        
        for (let i = 0; i < 30; i++) {
            const checkDate = new Date(today);
            checkDate.setDate(today.getDate() - i);
            const dayString = checkDate.toDateString();
            
            if (reviewsByDay[dayString]) {
                streak++;
            } else {
                break;
            }
        }

        return streak;
    }

    groupReviewsIntoSessions(reviews) {
        if (reviews.length === 0) return [];

        const sessions = [];
        let currentSession = null;
        const sessionGapMs = 60 * 60 * 1000; // 1 hour

        reviews.sort((a, b) => a.timestamp - b.timestamp);

        reviews.forEach(review => {
            if (!currentSession || review.timestamp - currentSession.lastTime > sessionGapMs) {
                // Start new session
                currentSession = {
                    startTime: review.timestamp,
                    lastTime: review.timestamp,
                    total: 1,
                    correct: review.rating >= 3 ? 1 : 0,
                    duration: 0
                };
                sessions.push(currentSession);
            } else {
                // Continue current session
                currentSession.lastTime = review.timestamp;
                currentSession.total++;
                if (review.rating >= 3) currentSession.correct++;
                currentSession.duration = currentSession.lastTime - currentSession.startTime;
            }
        });

        return sessions.reverse(); // Most recent first
    }

    formatDuration(ms) {
        const minutes = Math.floor(ms / (60 * 1000));
        return `${minutes}min`;
    }

    getTypeLabel(type) {
        const labels = {
            term: 'Términos',
            sentence: 'Frases',
            conjugation: 'Conjugaciones',
            listening: 'Escucha'
        };
        return labels[type] || type;
    }

    /**
     * Export user data
     */
    async exportData() {
        try {
            const data = await db.exportData();
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `catalanpro-backup-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            
            URL.revokeObjectURL(url);
            window.app.showToast('Datos exportados correctamente', 'success');
            
        } catch (error) {
            console.error('Error exporting data:', error);
            window.app.showToast('Error al exportar datos', 'error');
        }
    }

    /**
     * Import user data
     */
    async importData() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = async (e) => {
            const file = e.target.files[0];
            if (!file) return;

            try {
                const text = await file.text();
                const data = JSON.parse(text);
                
                if (!confirm('¿Estás seguro? Esto reemplazará todos los datos actuales.')) {
                    return;
                }
                
                await db.importData(data);
                window.app.showToast('Datos importados correctamente', 'success');
                
                // Refresh analytics
                await this.loadAnalyticsData();
                
            } catch (error) {
                console.error('Error importing data:', error);
                window.app.showToast('Error al importar datos', 'error');
            }
        };
        
        input.click();
    }
}

// Register globally
window.AnalyticsUI = AnalyticsUI;
