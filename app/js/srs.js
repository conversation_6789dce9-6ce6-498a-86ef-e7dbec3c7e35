// Spaced Repetition System (SRS) Engine
// FSRS-inspired algorithm with stability and difficulty tracking

class SRSEngine {
    constructor() {
        // FSRS parameters
        this.params = {
            // Learning steps (minutes)
            learningSteps: [1, 10],
            relearningSteps: [10],
            
            // Graduating intervals
            graduatingInterval: 1, // days
            easyInterval: 4, // days
            
            // Interval modifiers
            intervalModifier: 1.0,
            easyBonus: 1.3,
            hardInterval: 1.2,
            
            // Stability parameters
            w: [0.4, 0.6, 2.4, 5.8, 4.93, 0.94, 0.86, 0.01, 1.49, 0.14, 0.94, 2.18, 0.05, 0.34, 1.26, 0.29, 2.61],
            
            // Difficulty parameters
            difficultyDecayFactor: -0.5,
            difficultyInitial: 4.0,
            
            // Leech threshold
            leechThreshold: 8,
            
            // Maximum interval (days)
            maxInterval: 36500 // ~100 years
        };
    }

    /**
     * Calculate next review for a card
     * @param {Object} card - Current card state
     * @param {number} rating - User rating (1=Again, 2=Hard, 3=Good, 4=Easy)
     * @param {number} now - Current timestamp
     * @returns {Object} Updated card state
     */
    scheduleNext(card, rating, now = Date.now()) {
        const state = { ...card };
        
        // Initialize new cards
        if (!state.stability) state.stability = this.params.difficultyInitial;
        if (!state.difficulty) state.difficulty = this.params.difficultyInitial;
        if (!state.interval) state.interval = 0;
        if (!state.lapses) state.lapses = 0;
        if (!state.leechCount) state.leechCount = 0;
        if (!state.ease) state.ease = 2.5;

        // Handle different card states
        if (state.interval === 0) {
            // New card
            return this.scheduleNewCard(state, rating, now);
        } else if (state.interval < 1) {
            // Learning card
            return this.scheduleLearningCard(state, rating, now);
        } else {
            // Review card
            return this.scheduleReviewCard(state, rating, now);
        }
    }

    scheduleNewCard(state, rating, now) {
        const newState = { ...state };
        
        switch (rating) {
            case 1: // Again
                newState.interval = this.params.learningSteps[0] / (24 * 60); // Convert to days
                newState.stability = this.calculateStability(newState, rating);
                break;
                
            case 2: // Hard
                newState.interval = this.params.learningSteps[0] / (24 * 60);
                newState.stability = this.calculateStability(newState, rating);
                break;
                
            case 3: // Good
                newState.interval = this.params.graduatingInterval;
                newState.stability = this.calculateStability(newState, rating);
                break;
                
            case 4: // Easy
                newState.interval = this.params.easyInterval;
                newState.stability = this.calculateStability(newState, rating);
                break;
        }
        
        newState.difficulty = this.updateDifficulty(newState.difficulty, rating);
        newState.due = now + (newState.interval * 24 * 60 * 60 * 1000);
        newState.updatedAt = now;
        
        return newState;
    }

    scheduleLearningCard(state, rating, now) {
        const newState = { ...state };
        const currentStepIndex = this.params.learningSteps.findIndex(
            step => Math.abs(state.interval - step / (24 * 60)) < 0.001
        );
        
        switch (rating) {
            case 1: // Again - restart learning
                newState.interval = this.params.learningSteps[0] / (24 * 60);
                newState.lapses += 1;
                break;
                
            case 2: // Hard - repeat current step
                // Stay at current step
                break;
                
            case 3: // Good - advance to next step or graduate
                if (currentStepIndex < this.params.learningSteps.length - 1) {
                    newState.interval = this.params.learningSteps[currentStepIndex + 1] / (24 * 60);
                } else {
                    // Graduate
                    newState.interval = this.params.graduatingInterval;
                }
                break;
                
            case 4: // Easy - graduate immediately
                newState.interval = this.params.easyInterval;
                break;
        }
        
        newState.stability = this.calculateStability(newState, rating);
        newState.difficulty = this.updateDifficulty(newState.difficulty, rating);
        newState.due = now + (newState.interval * 24 * 60 * 60 * 1000);
        newState.updatedAt = now;
        
        // Check for leech
        if (newState.lapses >= this.params.leechThreshold) {
            newState.leechCount += 1;
        }
        
        return newState;
    }

    scheduleReviewCard(state, rating, now) {
        const newState = { ...state };
        const daysSinceLastReview = (now - (state.due - state.interval * 24 * 60 * 60 * 1000)) / (24 * 60 * 60 * 1000);
        
        switch (rating) {
            case 1: // Again - enter relearning
                newState.interval = this.params.relearningSteps[0] / (24 * 60);
                newState.lapses += 1;
                break;
                
            case 2: // Hard
                newState.interval = Math.max(
                    state.interval * this.params.hardInterval,
                    state.interval + 1
                );
                break;
                
            case 3: // Good
                newState.interval = state.interval * this.calculateIntervalModifier(state, daysSinceLastReview);
                break;
                
            case 4: // Easy
                newState.interval = state.interval * this.calculateIntervalModifier(state, daysSinceLastReview) * this.params.easyBonus;
                break;
        }
        
        // Apply constraints
        newState.interval = Math.min(newState.interval, this.params.maxInterval);
        newState.interval = Math.max(newState.interval, 1);
        
        newState.stability = this.calculateStability(newState, rating);
        newState.difficulty = this.updateDifficulty(newState.difficulty, rating);
        newState.due = now + (newState.interval * 24 * 60 * 60 * 1000);
        newState.updatedAt = now;
        
        // Check for leech
        if (newState.lapses >= this.params.leechThreshold) {
            newState.leechCount += 1;
        }
        
        return newState;
    }

    calculateStability(state, rating) {
        const w = this.params.w;
        
        if (state.interval === 0) {
            // New card stability
            return w[rating - 1];
        } else {
            // Review card stability
            const factor = Math.exp(w[8]) * 
                          (11 - state.difficulty) * 
                          Math.pow(state.stability, -w[9]) * 
                          (Math.exp((1 - rating) * w[10]) - 1);
            
            return state.stability * (1 + factor);
        }
    }

    updateDifficulty(currentDifficulty, rating) {
        const difficultyChange = this.params.difficultyDecayFactor * (rating - 3);
        const newDifficulty = currentDifficulty + difficultyChange;
        
        // Constrain difficulty between 1 and 10
        return Math.max(1, Math.min(10, newDifficulty));
    }

    calculateIntervalModifier(state, daysSinceLastReview) {
        // FSRS retention calculation
        const retention = Math.exp(-daysSinceLastReview / state.stability);
        return this.params.intervalModifier * (1 + (retention - 0.9) * 2);
    }

    /**
     * Get optimal retention rate for a card
     * @param {Object} card - Card state
     * @returns {number} Optimal retention rate (0-1)
     */
    getOptimalRetention(card) {
        // Simplified optimal retention calculation
        // In practice, this would consider user preferences and card importance
        return 0.9;
    }

    /**
     * Calculate predicted retention for a given interval
     * @param {Object} card - Card state
     * @param {number} interval - Interval in days
     * @returns {number} Predicted retention rate (0-1)
     */
    predictRetention(card, interval) {
        if (!card.stability) return 0.9;
        return Math.exp(-interval / card.stability);
    }

    /**
     * Get next review time for optimal retention
     * @param {Object} card - Card state
     * @returns {number} Optimal interval in days
     */
    getOptimalInterval(card) {
        const targetRetention = this.getOptimalRetention(card);
        if (!card.stability) return 1;
        
        return card.stability * Math.log(1 / targetRetention);
    }
}

// Test harness for SRS engine
class SRSTest {
    constructor() {
        this.srs = new SRSEngine();
    }

    runTests() {
        console.log('Running SRS tests...');
        
        this.testNewCard();
        this.testLearningProgression();
        this.testReviewProgression();
        this.testLeechDetection();
        
        console.log('SRS tests completed');
    }

    testNewCard() {
        console.log('Testing new card scheduling...');
        
        const newCard = {
            id: 'test-1',
            interval: 0,
            stability: null,
            difficulty: null,
            lapses: 0
        };
        
        // Test all ratings for new card
        for (let rating = 1; rating <= 4; rating++) {
            const result = this.srs.scheduleNext(newCard, rating);
            console.log(`New card rating ${rating}:`, {
                interval: result.interval,
                stability: result.stability.toFixed(2),
                difficulty: result.difficulty.toFixed(2)
            });
        }
    }

    testLearningProgression() {
        console.log('Testing learning progression...');
        
        let card = {
            id: 'test-2',
            interval: 0,
            stability: null,
            difficulty: null,
            lapses: 0
        };
        
        // Simulate learning progression with good ratings
        const ratings = [3, 3, 3]; // Good ratings to graduate
        
        for (let i = 0; i < ratings.length; i++) {
            card = this.srs.scheduleNext(card, ratings[i]);
            console.log(`Learning step ${i + 1}:`, {
                interval: card.interval.toFixed(3),
                stability: card.stability.toFixed(2),
                difficulty: card.difficulty.toFixed(2)
            });
        }
    }

    testReviewProgression() {
        console.log('Testing review progression...');
        
        let card = {
            id: 'test-3',
            interval: 1,
            stability: 2.4,
            difficulty: 4.0,
            lapses: 0,
            due: Date.now() - (24 * 60 * 60 * 1000) // Due yesterday
        };
        
        // Simulate several reviews with good ratings
        for (let i = 0; i < 5; i++) {
            card = this.srs.scheduleNext(card, 3); // Good rating
            console.log(`Review ${i + 1}:`, {
                interval: card.interval.toFixed(1),
                stability: card.stability.toFixed(2),
                difficulty: card.difficulty.toFixed(2)
            });
        }
    }

    testLeechDetection() {
        console.log('Testing leech detection...');
        
        let card = {
            id: 'test-4',
            interval: 1,
            stability: 2.4,
            difficulty: 4.0,
            lapses: 0,
            due: Date.now()
        };
        
        // Simulate many failures to trigger leech
        for (let i = 0; i < 10; i++) {
            card = this.srs.scheduleNext(card, 1); // Again rating
            console.log(`Failure ${i + 1}:`, {
                lapses: card.lapses,
                leechCount: card.leechCount,
                interval: card.interval.toFixed(3)
            });
        }
    }
}

// Create global SRS instance
const srs = new SRSEngine();
const srsTest = new SRSTest();

// Export for use in other modules
window.srs = srs;
window.srsTest = srsTest;
