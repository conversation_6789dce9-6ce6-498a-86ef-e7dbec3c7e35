// Review UI Module
// Handles all review modes and card presentation

class ReviewUI {
    constructor() {
        this.currentSession = null;
        this.currentCard = null;
        this.currentCardIndex = 0;
        this.sessionCards = [];
        this.sessionType = 'review'; // 'review', 'new', 'deck'
        this.reviewMode = 'standard'; // 'standard', 'reverse', 'listening', 'dictation', 'cloze', 'speed'
        this.showAnswer = false;
        this.startTime = null;
        this.audioSpeed = 'normal';
        this.sessionStats = {
            total: 0,
            completed: 0,
            correct: 0,
            again: 0,
            hard: 0,
            good: 0,
            easy: 0
        };
    }

    /**
     * Initialize the review UI
     */
    async init() {
        console.log('Initializing Review UI');
        this.setupEventHandlers();
        this.setupKeyboardShortcuts();
        await this.loadSettings();
    }

    /**
     * Set up event handlers
     */
    setupEventHandlers() {
        // Rating buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-rating]')) {
                const rating = parseInt(e.target.dataset.rating);
                this.rateCard(rating);
            }
        });

        // Show answer button
        document.addEventListener('click', (e) => {
            if (e.target.matches('#show-answer')) {
                this.toggleAnswer();
            }
        });

        // Audio controls
        document.addEventListener('click', (e) => {
            if (e.target.matches('#play-audio')) {
                this.playAudio();
            }
            if (e.target.matches('#audio-speed')) {
                this.toggleAudioSpeed();
            }
        });

        // Mode selector
        document.addEventListener('change', (e) => {
            if (e.target.matches('#review-mode')) {
                this.changeReviewMode(e.target.value);
            }
        });

        // Session controls
        document.addEventListener('click', (e) => {
            if (e.target.matches('#end-session')) {
                this.endSession();
            }
            if (e.target.matches('#pause-session')) {
                this.pauseSession();
            }
        });
    }

    /**
     * Set up keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (!this.currentSession) return;

            // Prevent shortcuts when typing in input fields
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                return;
            }

            switch (e.key) {
                case '1':
                    e.preventDefault();
                    this.rateCard(1);
                    break;
                case '2':
                    e.preventDefault();
                    this.rateCard(2);
                    break;
                case '3':
                    e.preventDefault();
                    this.rateCard(3);
                    break;
                case '4':
                    e.preventDefault();
                    this.rateCard(4);
                    break;
                case ' ':
                    e.preventDefault();
                    this.toggleAnswer();
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (this.showAnswer) {
                        this.rateCard(3); // Default to "Good"
                    } else {
                        this.toggleAnswer();
                    }
                    break;
                case 'a':
                case 'A':
                    e.preventDefault();
                    this.playAudio();
                    break;
                case 's':
                case 'S':
                    e.preventDefault();
                    this.toggleAudioSpeed();
                    break;
                case 'Escape':
                    e.preventDefault();
                    this.endSession();
                    break;
            }
        });
    }

    /**
     * Load user settings
     */
    async loadSettings() {
        try {
            this.audioSpeed = await db.getSetting('audioSpeedPreference', 'normal');
            this.reviewMode = await db.getSetting('defaultReviewMode', 'standard');
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }

    /**
     * Show the review view
     */
    async show() {
        if (!this.currentSession) {
            this.showSessionSelector();
        }
    }

    /**
     * Start a review session
     */
    async startSession(type = 'review', options = {}) {
        try {
            this.sessionType = type;
            this.sessionStats = {
                total: 0,
                completed: 0,
                correct: 0,
                again: 0,
                hard: 0,
                good: 0,
                easy: 0
            };

            // Load cards based on session type
            await this.loadSessionCards(type, options);

            if (this.sessionCards.length === 0) {
                this.showNoCardsMessage(type);
                return;
            }

            this.sessionStats.total = this.sessionCards.length;
            this.currentCardIndex = 0;
            this.currentSession = {
                type,
                options,
                startTime: Date.now()
            };

            this.renderReviewInterface();
            await this.showNextCard();

        } catch (error) {
            console.error('Error starting session:', error);
            window.app.showToast('Error al iniciar la sesión', 'error');
        }
    }

    /**
     * Load cards for the session
     */
    async loadSessionCards(type, options) {
        switch (type) {
            case 'review':
                this.sessionCards = await db.getDueCards(100);
                break;
            case 'new':
                const dailyLimit = await db.getSetting('dailyNewLimit', 20);
                this.sessionCards = await db.getNewCards(dailyLimit);
                break;
            case 'deck':
                if (options.deckId) {
                    this.sessionCards = await db.getCardsByDeck(options.deckId);
                    // Filter for due cards only
                    const now = Date.now();
                    this.sessionCards = this.sessionCards.filter(card => 
                        card.due <= now || card.interval === 0
                    );
                }
                break;
            default:
                this.sessionCards = [];
        }

        // Shuffle cards for variety
        this.shuffleArray(this.sessionCards);
    }

    /**
     * Render the review interface
     */
    renderReviewInterface() {
        const reviewView = document.getElementById('review-view');
        if (!reviewView) return;

        reviewView.innerHTML = `
            <div class="review-container">
                <div class="review-header">
                    <div class="session-info">
                        <span class="session-type">${this.getSessionTypeLabel()}</span>
                        <span class="progress">${this.sessionStats.completed}/${this.sessionStats.total}</span>
                    </div>
                    <div class="review-controls">
                        <select id="review-mode" class="mode-selector">
                            <option value="standard" ${this.reviewMode === 'standard' ? 'selected' : ''}>Estándar</option>
                            <option value="reverse" ${this.reviewMode === 'reverse' ? 'selected' : ''}>Inverso</option>
                            <option value="listening" ${this.reviewMode === 'listening' ? 'selected' : ''}>Escucha</option>
                            <option value="dictation" ${this.reviewMode === 'dictation' ? 'selected' : ''}>Dictado</option>
                            <option value="cloze" ${this.reviewMode === 'cloze' ? 'selected' : ''}>Completar</option>
                            <option value="speed" ${this.reviewMode === 'speed' ? 'selected' : ''}>Velocidad</option>
                        </select>
                        <button id="pause-session" class="btn btn-outline">⏸️</button>
                        <button id="end-session" class="btn btn-outline">❌</button>
                    </div>
                </div>

                <div class="card-container">
                    <div id="card-content" class="card-content">
                        <!-- Card content will be rendered here -->
                    </div>
                </div>

                <div class="review-actions">
                    <div id="answer-section" class="answer-section hidden">
                        <div class="rating-buttons">
                            <button class="rating-btn again" data-rating="1">
                                <span class="rating-label">Otra vez</span>
                                <span class="rating-key">1</span>
                            </button>
                            <button class="rating-btn hard" data-rating="2">
                                <span class="rating-label">Difícil</span>
                                <span class="rating-key">2</span>
                            </button>
                            <button class="rating-btn good" data-rating="3">
                                <span class="rating-label">Bien</span>
                                <span class="rating-key">3</span>
                            </button>
                            <button class="rating-btn easy" data-rating="4">
                                <span class="rating-label">Fácil</span>
                                <span class="rating-key">4</span>
                            </button>
                        </div>
                    </div>
                    
                    <div id="question-section" class="question-section">
                        <button id="show-answer" class="btn btn-primary btn-large">
                            Mostrar respuesta
                            <span class="shortcut">Espacio</span>
                        </button>
                    </div>
                </div>

                <div class="session-stats">
                    <div class="stat">
                        <span class="stat-label">Correctas</span>
                        <span class="stat-value" id="correct-count">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Tiempo</span>
                        <span class="stat-value" id="session-time">0:00</span>
                    </div>
                </div>
            </div>
        `;

        // Start session timer
        this.startSessionTimer();
    }

    /**
     * Show next card in the session
     */
    async showNextCard() {
        if (this.currentCardIndex >= this.sessionCards.length) {
            this.completeSession();
            return;
        }

        this.currentCard = this.sessionCards[this.currentCardIndex];
        this.showAnswer = false;
        this.startTime = Date.now();

        this.renderCard();
        this.updateProgress();
        
        // Hide answer section, show question section
        this.toggleAnswerVisibility(false);
    }

    /**
     * Render the current card based on review mode
     */
    renderCard() {
        const cardContent = document.getElementById('card-content');
        if (!cardContent || !this.currentCard) return;

        switch (this.reviewMode) {
            case 'standard':
                this.renderStandardCard();
                break;
            case 'reverse':
                this.renderReverseCard();
                break;
            case 'listening':
                this.renderListeningCard();
                break;
            case 'dictation':
                this.renderDictationCard();
                break;
            case 'cloze':
                this.renderClozeCard();
                break;
            case 'speed':
                this.renderSpeedCard();
                break;
            default:
                this.renderStandardCard();
        }
    }

    /**
     * Render standard mode (Catalan → Translation)
     */
    renderStandardCard() {
        const card = this.currentCard;
        const cardContent = document.getElementById('card-content');
        
        cardContent.innerHTML = `
            <div class="card-question">
                <div class="card-text-main">${this.escapeHtml(card.catalan)}</div>
                ${card.ipa ? `<div class="card-ipa">[${this.escapeHtml(card.ipa)}]</div>` : ''}
                ${this.renderAudioControls()}
            </div>
            <div class="card-answer ${this.showAnswer ? '' : 'hidden'}">
                ${this.renderTranslations()}
                ${this.renderExamples()}
                ${this.renderGrammarTips()}
            </div>
        `;
    }

    /**
     * Render reverse mode (Translation → Catalan)
     */
    renderReverseCard() {
        const card = this.currentCard;
        const cardContent = document.getElementById('card-content');
        const visibleLangs = ['es', 'en']; // TODO: Get from settings
        
        // Pick a random translation to show
        const availableTranslations = visibleLangs.filter(lang => card.translations[lang]);
        const randomLang = availableTranslations[Math.floor(Math.random() * availableTranslations.length)];
        const translation = card.translations[randomLang] || card.translations.es || '';
        
        cardContent.innerHTML = `
            <div class="card-question">
                <div class="card-text-main">${this.escapeHtml(translation)}</div>
                <div class="card-hint">Tradueix al català</div>
            </div>
            <div class="card-answer ${this.showAnswer ? '' : 'hidden'}">
                <div class="card-text-main">${this.escapeHtml(card.catalan)}</div>
                ${card.ipa ? `<div class="card-ipa">[${this.escapeHtml(card.ipa)}]</div>` : ''}
                ${this.renderAudioControls()}
                ${this.renderExamples()}
            </div>
        `;
    }

    /**
     * Render listening mode (Audio → Meaning)
     */
    renderListeningCard() {
        const card = this.currentCard;
        const cardContent = document.getElementById('card-content');
        
        cardContent.innerHTML = `
            <div class="card-question">
                <div class="listening-prompt">🎧 Escolta i selecciona el significat</div>
                ${this.renderAudioControls(true)}
            </div>
            <div class="card-answer ${this.showAnswer ? '' : 'hidden'}">
                <div class="card-text-main">${this.escapeHtml(card.catalan)}</div>
                ${this.renderTranslations()}
                ${this.renderExamples()}
            </div>
        `;
        
        // Auto-play audio in listening mode
        setTimeout(() => this.playAudio(), 500);
    }

    /**
     * Render audio controls
     */
    renderAudioControls(prominent = false) {
        const card = this.currentCard;
        if (!card.audioIds || card.audioIds.length === 0) {
            return '';
        }

        const className = prominent ? 'audio-controls prominent' : 'audio-controls';
        
        return `
            <div class="${className}">
                <button id="play-audio" class="audio-btn">
                    🔊 Reproduir
                </button>
                <button id="audio-speed" class="audio-speed-btn">
                    ${this.audioSpeed === 'slow' ? '🐌' : '🏃'} ${this.audioSpeed}
                </button>
            </div>
        `;
    }

    /**
     * Render translations
     */
    renderTranslations() {
        const card = this.currentCard;
        const visibleLangs = ['es', 'en', 'ru']; // TODO: Get from settings
        
        const translations = visibleLangs
            .filter(lang => card.translations[lang])
            .map(lang => `
                <div class="translation">
                    <span class="lang-code">${lang.toUpperCase()}</span>
                    <span class="translation-text">${this.escapeHtml(card.translations[lang])}</span>
                </div>
            `).join('');
        
        return translations ? `<div class="translations">${translations}</div>` : '';
    }

    /**
     * Render examples
     */
    renderExamples() {
        const card = this.currentCard;
        if (!card.examples || card.examples.length === 0) return '';
        
        const examples = card.examples.slice(0, 2).map(example => 
            `<div class="example">${this.escapeHtml(example)}</div>`
        ).join('');
        
        return `<div class="examples"><strong>Exemples:</strong>${examples}</div>`;
    }

    /**
     * Render grammar tips
     */
    renderGrammarTips() {
        const card = this.currentCard;
        if (!card.grammarTips || card.grammarTips.length === 0) return '';
        
        const tips = card.grammarTips.slice(0, 1).map(tip => 
            `<div class="grammar-tip">${this.escapeHtml(tip)}</div>`
        ).join('');
        
        return `<div class="grammar-tips"><strong>Gramàtica:</strong>${tips}</div>`;
    }

    /**
     * Toggle answer visibility
     */
    toggleAnswer() {
        this.showAnswer = !this.showAnswer;
        this.toggleAnswerVisibility(this.showAnswer);
        this.renderCard(); // Re-render to show/hide answer content
    }

    /**
     * Toggle answer section visibility
     */
    toggleAnswerVisibility(show) {
        const answerSection = document.getElementById('answer-section');
        const questionSection = document.getElementById('question-section');
        
        if (show) {
            answerSection?.classList.remove('hidden');
            questionSection?.classList.add('hidden');
        } else {
            answerSection?.classList.add('hidden');
            questionSection?.classList.remove('hidden');
        }
    }

    /**
     * Rate the current card
     */
    async rateCard(rating) {
        if (!this.currentCard || !this.showAnswer) return;

        try {
            const elapsedMs = Date.now() - this.startTime;
            const prevState = { ...this.currentCard };
            
            // Calculate new SRS state
            const newState = srs.scheduleNext(this.currentCard, rating);
            
            // Update card in database
            await db.cards.update(this.currentCard.id, newState);
            
            // Log the review
            await db.logReview(this.currentCard.id, rating, elapsedMs, prevState, newState);
            
            // Update session stats
            this.updateSessionStats(rating);
            
            // Move to next card
            this.currentCardIndex++;
            await this.showNextCard();

        } catch (error) {
            console.error('Error rating card:', error);
            window.app.showToast('Error al calificar la tarjeta', 'error');
        }
    }

    /**
     * Update session statistics
     */
    updateSessionStats(rating) {
        this.sessionStats.completed++;
        
        switch (rating) {
            case 1: this.sessionStats.again++; break;
            case 2: this.sessionStats.hard++; break;
            case 3: this.sessionStats.good++; this.sessionStats.correct++; break;
            case 4: this.sessionStats.easy++; this.sessionStats.correct++; break;
        }
        
        // Update display
        const correctCount = document.getElementById('correct-count');
        if (correctCount) {
            correctCount.textContent = this.sessionStats.correct;
        }
    }

    /**
     * Update progress display
     */
    updateProgress() {
        const progressEl = document.querySelector('.progress');
        if (progressEl) {
            progressEl.textContent = `${this.sessionStats.completed}/${this.sessionStats.total}`;
        }
    }

    /**
     * Utility functions
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }

    getSessionTypeLabel() {
        switch (this.sessionType) {
            case 'review': return 'Repaso';
            case 'new': return 'Nuevas';
            case 'deck': return 'Mazo';
            default: return 'Sesión';
        }
    }

    // Placeholder methods for remaining functionality
    renderDictationCard() { this.renderStandardCard(); }
    renderClozeCard() { this.renderStandardCard(); }
    renderSpeedCard() { this.renderStandardCard(); }
    
    playAudio() { console.log('Playing audio...'); }
    toggleAudioSpeed() { 
        this.audioSpeed = this.audioSpeed === 'normal' ? 'slow' : 'normal';
        this.renderCard();
    }
    
    changeReviewMode(mode) { 
        this.reviewMode = mode;
        this.renderCard();
    }
    
    pauseSession() { console.log('Session paused'); }
    endSession() { window.app.showView('dashboard'); }
    completeSession() { 
        window.app.showToast('¡Sesión completada!', 'success');
        window.app.showView('dashboard');
    }
    
    showSessionSelector() {
        const reviewView = document.getElementById('review-view');
        if (reviewView) {
            reviewView.innerHTML = '<div class="session-selector"><h2>Selecciona tipo de sesión</h2></div>';
        }
    }
    
    showNoCardsMessage(type) {
        window.app.showToast('No hay tarjetas disponibles para esta sesión', 'info');
        window.app.showView('dashboard');
    }
    
    startSessionTimer() {
        setInterval(() => {
            if (this.currentSession) {
                const elapsed = Math.floor((Date.now() - this.currentSession.startTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                const timeEl = document.getElementById('session-time');
                if (timeEl) {
                    timeEl.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                }
            }
        }, 1000);
    }
}

// Register globally
window.ReviewUI = ReviewUI;
