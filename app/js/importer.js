// Streaming JSONL Importer with Checkpoints and Resume
// Handles large dataset imports with progress tracking and recovery

class JSONLImporter {
    constructor() {
        this.isImporting = false;
        this.isPaused = false;
        this.currentFile = null;
        this.totalFiles = 0;
        this.completedFiles = 0;
        this.batchSize = 500; // Records per transaction
        this.progressCallback = null;
        this.logCallback = null;
        
        // Files to import in order
        this.importFiles = [
            { name: 'decks_topics.jsonl', table: 'decks', description: 'Mazos y temas' },
            { name: 'media_manifest.jsonl', table: 'media', description: 'Archivos de audio' },
            { name: 'cards_terms_A1.jsonl', table: 'cards', description: 'Vocabulario A1' },
            { name: 'cards_terms_A2.jsonl', table: 'cards', description: 'Vocabulario A2' },
            { name: 'cards_terms_B1.jsonl', table: 'cards', description: 'Vocabulario B1' },
            { name: 'cards_terms_B2.jsonl', table: 'cards', description: 'Vocabulario B2' },
            { name: 'cards_terms_C1.jsonl', table: 'cards', description: 'Vocabulario C1' },
            { name: 'cards_terms_C2.jsonl', table: 'cards', description: 'Vocabulario C2' },
            { name: 'cards_sentences_A1.jsonl', table: 'cards', description: 'Frases A1' },
            { name: 'cards_sentences_A2.jsonl', table: 'cards', description: 'Frases A2' },
            { name: 'cards_sentences_B1.jsonl', table: 'cards', description: 'Frases B1' },
            { name: 'cards_sentences_B2.jsonl', table: 'cards', description: 'Frases B2' },
            { name: 'cards_sentences_C1.jsonl', table: 'cards', description: 'Frases C1' },
            { name: 'cards_sentences_C2.jsonl', table: 'cards', description: 'Frases C2' },
            { name: 'cards_conjugations.jsonl', table: 'cards', description: 'Conjugaciones' }
        ];
    }

    /**
     * Start the import process
     * @param {Function} progressCallback - Progress update callback
     * @param {Function} logCallback - Log message callback
     */
    async startImport(progressCallback, logCallback) {
        if (this.isImporting) {
            throw new Error('Import already in progress');
        }

        this.progressCallback = progressCallback;
        this.logCallback = logCallback;
        this.isImporting = true;
        this.isPaused = false;

        try {
            // Check for existing checkpoint
            const checkpoint = await db.getImportCheckpoint();
            let startFileIndex = 0;
            let startLineNumber = 0;

            if (checkpoint) {
                this.log(`Reanudando desde: ${checkpoint.filename}, línea ${checkpoint.lineNumber}`);
                startFileIndex = this.importFiles.findIndex(f => f.name === checkpoint.filename);
                startLineNumber = checkpoint.lineNumber;
                
                if (startFileIndex === -1) {
                    this.log('Checkpoint inválido, empezando desde el principio');
                    startFileIndex = 0;
                    startLineNumber = 0;
                }
            } else {
                this.log('Iniciando importación completa');
                // Clear existing data
                await this.clearExistingData();
            }

            this.totalFiles = this.importFiles.length;
            this.completedFiles = startFileIndex;

            // Import files starting from checkpoint
            for (let i = startFileIndex; i < this.importFiles.length; i++) {
                if (this.isPaused) {
                    this.log('Importación pausada');
                    return;
                }

                const fileInfo = this.importFiles[i];
                const lineStart = (i === startFileIndex) ? startLineNumber : 0;
                
                await this.importFile(fileInfo, lineStart);
                this.completedFiles = i + 1;
                
                // Clear checkpoint after successful file completion
                if (i === startFileIndex && startLineNumber > 0) {
                    await db.clearImportCheckpoint();
                }
            }

            // Import completed successfully
            await this.completeImport();

        } catch (error) {
            this.log(`Error durante la importación: ${error.message}`);
            throw error;
        } finally {
            this.isImporting = false;
        }
    }

    /**
     * Import a single JSONL file
     * @param {Object} fileInfo - File information
     * @param {number} startLine - Line to start from (for resume)
     */
    async importFile(fileInfo, startLine = 0) {
        this.currentFile = fileInfo.name;
        this.log(`Importando ${fileInfo.description} (${fileInfo.name})`);

        try {
            const response = await fetch(`data/${fileInfo.name}`);
            if (!response.ok) {
                if (response.status === 404) {
                    this.log(`Archivo no encontrado: ${fileInfo.name} (omitiendo)`);
                    return;
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            
            let buffer = '';
            let lineNumber = 0;
            let batch = [];
            let totalProcessed = 0;

            while (true) {
                if (this.isPaused) {
                    // Save checkpoint before pausing
                    await db.setImportCheckpoint({
                        filename: fileInfo.name,
                        lineNumber: lineNumber
                    });
                    return;
                }

                const { done, value } = await reader.read();
                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop(); // Keep incomplete line in buffer

                for (const line of lines) {
                    lineNumber++;
                    
                    // Skip lines before start position (for resume)
                    if (lineNumber <= startLine) {
                        continue;
                    }

                    if (line.trim()) {
                        try {
                            const record = JSON.parse(line);
                            batch.push(record);
                            totalProcessed++;

                            // Process batch when full
                            if (batch.length >= this.batchSize) {
                                await this.processBatch(fileInfo.table, batch);
                                batch = [];
                                
                                // Save checkpoint
                                await db.setImportCheckpoint({
                                    filename: fileInfo.name,
                                    lineNumber: lineNumber
                                });

                                // Update progress
                                this.updateProgress(fileInfo.name, totalProcessed);
                            }
                        } catch (parseError) {
                            this.log(`Error parsing line ${lineNumber}: ${parseError.message}`);
                        }
                    }
                }
            }

            // Process remaining records in batch
            if (batch.length > 0) {
                await this.processBatch(fileInfo.table, batch);
            }

            this.log(`Completado ${fileInfo.description}: ${totalProcessed} registros`);

        } catch (error) {
            this.log(`Error importando ${fileInfo.name}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Process a batch of records
     * @param {string} tableName - Target table name
     * @param {Array} records - Records to insert
     */
    async processBatch(tableName, records) {
        try {
            const table = db[tableName];
            if (!table) {
                throw new Error(`Unknown table: ${tableName}`);
            }

            await table.bulkAdd(records);
        } catch (error) {
            // Handle duplicate key errors gracefully
            if (error.name === 'BulkError') {
                const successCount = records.length - error.failures.length;
                this.log(`Batch partially imported: ${successCount}/${records.length} records`);
            } else {
                throw error;
            }
        }
    }

    /**
     * Clear existing data before import
     */
    async clearExistingData() {
        this.log('Limpiando datos existentes...');
        
        await db.transaction('rw', db.cards, db.decks, db.media, async () => {
            await Promise.all([
                db.cards.clear(),
                db.decks.clear(),
                db.media.clear()
            ]);
        });
    }

    /**
     * Complete the import process
     */
    async completeImport() {
        this.log('Ejecutando verificaciones de integridad...');
        
        // Run data validation
        const validation = await db.validateData();
        
        if (validation.valid) {
            this.log('✅ Importación completada exitosamente');
            this.log(`📊 Estadísticas: ${validation.cardCount} tarjetas, ${validation.deckCount} mazos, ${validation.mediaCount} archivos de audio`);
        } else {
            this.log('⚠️ Importación completada con advertencias:');
            validation.issues.forEach(issue => this.log(`  - ${issue}`));
        }

        // Mark as initialized
        await db.setInitialized(true);
        await db.clearImportCheckpoint();

        // Generate QA report
        const qaReport = await this.generateQAReport(validation);
        this.log('📋 Reporte de calidad generado');
        
        return qaReport;
    }

    /**
     * Generate quality assurance report
     * @param {Object} validation - Validation results
     */
    async generateQAReport(validation) {
        const [cardCounts, deckCounts] = await Promise.all([
            db.getCardCounts(),
            db.getDeckCounts()
        ]);

        // Count cards by type and CEFR level
        const cardsByType = {};
        const cardsByCEFR = {};
        
        const allCards = await db.cards.toArray();
        
        for (const card of allCards) {
            cardsByType[card.type] = (cardsByType[card.type] || 0) + 1;
            cardsByCEFR[card.cefr] = (cardsByCEFR[card.cefr] || 0) + 1;
        }

        const report = {
            timestamp: new Date().toISOString(),
            validation,
            statistics: {
                total: cardCounts,
                byType: cardsByType,
                byCEFR: cardsByCEFR,
                byDeck: deckCounts
            },
            files: this.importFiles.map(f => ({
                name: f.name,
                description: f.description,
                imported: true // TODO: Track actual import status
            }))
        };

        // Save report to settings
        await db.setSetting('lastQAReport', report);
        
        return report;
    }

    /**
     * Pause the import process
     */
    pause() {
        if (this.isImporting) {
            this.isPaused = true;
            this.log('Pausando importación...');
        }
    }

    /**
     * Resume the import process
     */
    async resume() {
        if (this.isImporting && this.isPaused) {
            this.isPaused = false;
            this.log('Reanudando importación...');
            // The import loop will continue automatically
        }
    }

    /**
     * Update progress and call callback
     * @param {string} filename - Current file
     * @param {number} processed - Records processed in current file
     */
    updateProgress(filename, processed) {
        if (this.progressCallback) {
            const fileProgress = Math.min(100, (processed / 1000) * 100); // Rough estimate
            const overallProgress = ((this.completedFiles / this.totalFiles) * 100);
            
            this.progressCallback({
                currentFile: filename,
                fileProgress,
                overallProgress,
                processedRecords: processed
            });
        }
    }

    /**
     * Log message and call callback
     * @param {string} message - Log message
     */
    log(message) {
        console.log(`[Importer] ${message}`);
        if (this.logCallback) {
            this.logCallback(message);
        }
    }
}

// Create global importer instance
const importer = new JSONLImporter();

// Export for use in other modules
window.importer = importer;
