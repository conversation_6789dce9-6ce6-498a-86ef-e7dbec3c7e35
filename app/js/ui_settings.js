// Settings UI Module
// Handles user preferences and app configuration

class SettingsUI {
    constructor() {
        this.settings = {};
    }

    /**
     * Initialize the settings UI
     */
    async init() {
        console.log('Initializing Settings UI');
        await this.loadSettings();
        this.setupEventHandlers();
    }

    /**
     * Show the settings view
     */
    async show() {
        await this.renderSettings();
        await this.loadSettings();
        this.populateForm();
    }

    /**
     * Load current settings
     */
    async loadSettings() {
        try {
            this.settings = {
                uiLanguage: await db.getSetting('uiLanguage', 'ca'),
                visibleTranslations: await db.getSetting('visibleTranslations', ['es', 'en']),
                dailyNewLimit: await db.getSetting('dailyNewLimit', 20),
                dailyReviewCap: await db.getSetting('dailyReviewCap', 100),
                audioSpeedPreference: await db.getSetting('audioSpeedPreference', 'normal'),
                theme: await db.getSetting('theme', 'light'),
                defaultReviewMode: await db.getSetting('defaultReviewMode', 'standard'),
                showKeyboardShortcuts: await db.getSetting('showKeyboardShortcuts', true),
                autoPlayAudio: await db.getSetting('autoPlayAudio', false),
                confirmBeforeExit: await db.getSetting('confirmBeforeExit', true)
            };
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }

    /**
     * Set up event handlers
     */
    setupEventHandlers() {
        document.addEventListener('change', (e) => {
            if (e.target.matches('.setting-input')) {
                this.saveSetting(e.target);
            }
        });

        document.addEventListener('click', (e) => {
            if (e.target.matches('#reset-settings')) {
                this.resetSettings();
            }
            if (e.target.matches('#clear-data')) {
                this.clearAllData();
            }
            if (e.target.matches('#restart-import')) {
                this.restartImport();
            }
            if (e.target.matches('#test-srs')) {
                this.testSRS();
            }
        });
    }

    /**
     * Render the settings interface
     */
    async renderSettings() {
        const settingsView = document.getElementById('settings-view');
        if (!settingsView) return;

        settingsView.innerHTML = `
            <div class="settings-container">
                <h1>Configuración</h1>

                <!-- Language Settings -->
                <div class="settings-section">
                    <h2>🌐 Idioma y Traducciones</h2>
                    
                    <div class="setting-item">
                        <label for="ui-language">Idioma de la interfaz</label>
                        <select id="ui-language" class="setting-input form-control">
                            <option value="ca">Català</option>
                            <option value="es">Español</option>
                            <option value="en">English</option>
                        </select>
                    </div>

                    <div class="setting-item">
                        <label>Traducciones visibles</label>
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="show-es" class="setting-input" value="es">
                                Español
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="show-en" class="setting-input" value="en">
                                English
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="show-ru" class="setting-input" value="ru">
                                Русский
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Study Settings -->
                <div class="settings-section">
                    <h2>📚 Configuración de Estudio</h2>
                    
                    <div class="setting-item">
                        <label for="daily-new-limit">Nuevas tarjetas por día</label>
                        <input type="number" id="daily-new-limit" class="setting-input form-control" min="1" max="100">
                        <div class="setting-help">Número máximo de tarjetas nuevas a estudiar cada día</div>
                    </div>

                    <div class="setting-item">
                        <label for="daily-review-cap">Límite de repasos diarios</label>
                        <input type="number" id="daily-review-cap" class="setting-input form-control" min="10" max="500">
                        <div class="setting-help">Número máximo de repasos por día</div>
                    </div>

                    <div class="setting-item">
                        <label for="default-review-mode">Modo de repaso por defecto</label>
                        <select id="default-review-mode" class="setting-input form-control">
                            <option value="standard">Estándar (CA → ES/EN)</option>
                            <option value="reverse">Inverso (ES/EN → CA)</option>
                            <option value="listening">Escucha</option>
                            <option value="mixed">Mixto</option>
                        </select>
                    </div>
                </div>

                <!-- Audio Settings -->
                <div class="settings-section">
                    <h2>🔊 Configuración de Audio</h2>
                    
                    <div class="setting-item">
                        <label for="audio-speed">Velocidad de audio preferida</label>
                        <select id="audio-speed" class="setting-input form-control">
                            <option value="slow">Lento</option>
                            <option value="normal">Normal</option>
                        </select>
                    </div>

                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="auto-play-audio" class="setting-input">
                            Reproducir audio automáticamente
                        </label>
                        <div class="setting-help">Reproduce audio al mostrar tarjetas de escucha</div>
                    </div>
                </div>

                <!-- Interface Settings -->
                <div class="settings-section">
                    <h2>🎨 Interfaz</h2>
                    
                    <div class="setting-item">
                        <label for="theme">Tema</label>
                        <select id="theme" class="setting-input form-control">
                            <option value="light">Claro</option>
                            <option value="dark">Oscuro</option>
                            <option value="auto">Automático</option>
                        </select>
                    </div>

                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="show-keyboard-shortcuts" class="setting-input">
                            Mostrar atajos de teclado
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="confirm-before-exit" class="setting-input">
                            Confirmar antes de salir de sesiones
                        </label>
                    </div>
                </div>

                <!-- Data Management -->
                <div class="settings-section">
                    <h2>💾 Gestión de Datos</h2>
                    
                    <div class="setting-item">
                        <div class="data-info">
                            <div class="info-item">
                                <span class="info-label">Estado de la base de datos:</span>
                                <span class="info-value" id="db-status">Cargando...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Última importación:</span>
                                <span class="info-value" id="last-import">Nunca</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Tamaño estimado:</span>
                                <span class="info-value" id="db-size">Calculando...</span>
                            </div>
                        </div>
                    </div>

                    <div class="setting-actions">
                        <button id="restart-import" class="btn btn-secondary">
                            <span class="btn-icon">🔄</span>
                            Reiniciar Importación
                        </button>
                        <button id="clear-data" class="btn btn-outline">
                            <span class="btn-icon">🗑️</span>
                            Limpiar Todos los Datos
                        </button>
                    </div>
                </div>

                <!-- Advanced Settings -->
                <div class="settings-section">
                    <h2>⚙️ Configuración Avanzada</h2>
                    
                    <div class="setting-item">
                        <button id="test-srs" class="btn btn-outline">
                            <span class="btn-icon">🧪</span>
                            Probar Sistema SRS
                        </button>
                        <div class="setting-help">Ejecuta pruebas del algoritmo de repetición espaciada</div>
                    </div>

                    <div class="setting-item">
                        <button id="reset-settings" class="btn btn-outline">
                            <span class="btn-icon">↩️</span>
                            Restaurar Configuración por Defecto
                        </button>
                    </div>
                </div>

                <!-- App Information -->
                <div class="settings-section">
                    <h2>ℹ️ Información de la App</h2>
                    
                    <div class="app-info">
                        <div class="info-item">
                            <span class="info-label">Versión:</span>
                            <span class="info-value">1.0.0</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Tipo:</span>
                            <span class="info-value">PWA Offline</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Base de datos:</span>
                            <span class="info-value">IndexedDB + Dexie.js</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">SRS:</span>
                            <span class="info-value">FSRS-inspired</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        await this.loadDatabaseInfo();
    }

    /**
     * Populate form with current settings
     */
    populateForm() {
        // UI Language
        const uiLangEl = document.getElementById('ui-language');
        if (uiLangEl) uiLangEl.value = this.settings.uiLanguage;

        // Visible translations
        const visibleTranslations = this.settings.visibleTranslations;
        document.getElementById('show-es').checked = visibleTranslations.includes('es');
        document.getElementById('show-en').checked = visibleTranslations.includes('en');
        document.getElementById('show-ru').checked = visibleTranslations.includes('ru');

        // Study settings
        document.getElementById('daily-new-limit').value = this.settings.dailyNewLimit;
        document.getElementById('daily-review-cap').value = this.settings.dailyReviewCap;
        document.getElementById('default-review-mode').value = this.settings.defaultReviewMode;

        // Audio settings
        document.getElementById('audio-speed').value = this.settings.audioSpeedPreference;
        document.getElementById('auto-play-audio').checked = this.settings.autoPlayAudio;

        // Interface settings
        document.getElementById('theme').value = this.settings.theme;
        document.getElementById('show-keyboard-shortcuts').checked = this.settings.showKeyboardShortcuts;
        document.getElementById('confirm-before-exit').checked = this.settings.confirmBeforeExit;
    }

    /**
     * Save individual setting
     */
    async saveSetting(element) {
        try {
            const settingName = this.getSettingName(element.id);
            let value;

            if (element.type === 'checkbox') {
                if (element.id.startsWith('show-')) {
                    // Handle visible translations
                    const visibleTranslations = [];
                    if (document.getElementById('show-es').checked) visibleTranslations.push('es');
                    if (document.getElementById('show-en').checked) visibleTranslations.push('en');
                    if (document.getElementById('show-ru').checked) visibleTranslations.push('ru');
                    
                    await db.setSetting('visibleTranslations', visibleTranslations);
                    this.settings.visibleTranslations = visibleTranslations;
                    return;
                } else {
                    value = element.checked;
                }
            } else if (element.type === 'number') {
                value = parseInt(element.value);
            } else {
                value = element.value;
            }

            await db.setSetting(settingName, value);
            this.settings[settingName] = value;

            // Apply setting immediately if needed
            this.applySettingChange(settingName, value);

            window.app.showToast('Configuración guardada', 'success');

        } catch (error) {
            console.error('Error saving setting:', error);
            window.app.showToast('Error al guardar configuración', 'error');
        }
    }

    /**
     * Get setting name from element ID
     */
    getSettingName(elementId) {
        const mapping = {
            'ui-language': 'uiLanguage',
            'daily-new-limit': 'dailyNewLimit',
            'daily-review-cap': 'dailyReviewCap',
            'default-review-mode': 'defaultReviewMode',
            'audio-speed': 'audioSpeedPreference',
            'auto-play-audio': 'autoPlayAudio',
            'theme': 'theme',
            'show-keyboard-shortcuts': 'showKeyboardShortcuts',
            'confirm-before-exit': 'confirmBeforeExit'
        };
        return mapping[elementId] || elementId;
    }

    /**
     * Apply setting change immediately
     */
    applySettingChange(settingName, value) {
        switch (settingName) {
            case 'theme':
                this.applyTheme(value);
                break;
            case 'uiLanguage':
                // Would need to reload interface with new language
                window.app.showToast('Reinicia la app para aplicar el cambio de idioma', 'info');
                break;
        }
    }

    /**
     * Apply theme
     */
    applyTheme(theme) {
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        
        if (theme === 'auto') {
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            theme = prefersDark ? 'dark' : 'light';
        }
        
        document.body.classList.add(`theme-${theme}`);
    }

    /**
     * Load database information
     */
    async loadDatabaseInfo() {
        try {
            const isInitialized = await db.isInitialized();
            const cardCount = await db.cards.count();
            const deckCount = await db.decks.count();
            
            document.getElementById('db-status').textContent = isInitialized ? 'Inicializada' : 'No inicializada';
            
            // Estimate database size (rough calculation)
            const estimatedSize = (cardCount * 1000 + deckCount * 500) / 1024; // KB
            document.getElementById('db-size').textContent = `~${estimatedSize.toFixed(1)} KB`;
            
            // Last import info
            const lastQAReport = await db.getSetting('lastQAReport');
            if (lastQAReport) {
                const date = new Date(lastQAReport.timestamp).toLocaleDateString();
                document.getElementById('last-import').textContent = date;
            }

        } catch (error) {
            console.error('Error loading database info:', error);
        }
    }

    /**
     * Reset all settings to defaults
     */
    async resetSettings() {
        if (!confirm('¿Estás seguro de que quieres restaurar toda la configuración por defecto?')) {
            return;
        }

        try {
            const defaultSettings = {
                uiLanguage: 'ca',
                visibleTranslations: ['es', 'en'],
                dailyNewLimit: 20,
                dailyReviewCap: 100,
                audioSpeedPreference: 'normal',
                theme: 'light',
                defaultReviewMode: 'standard',
                showKeyboardShortcuts: true,
                autoPlayAudio: false,
                confirmBeforeExit: true
            };

            for (const [key, value] of Object.entries(defaultSettings)) {
                await db.setSetting(key, value);
            }

            this.settings = defaultSettings;
            this.populateForm();
            
            window.app.showToast('Configuración restaurada', 'success');

        } catch (error) {
            console.error('Error resetting settings:', error);
            window.app.showToast('Error al restaurar configuración', 'error');
        }
    }

    /**
     * Clear all data
     */
    async clearAllData() {
        const confirmation = prompt('Escribe "BORRAR" para confirmar que quieres eliminar todos los datos:');
        if (confirmation !== 'BORRAR') {
            return;
        }

        try {
            await db.transaction('rw', db.cards, db.decks, db.media, db.reviewLog, db.settings, async () => {
                await Promise.all([
                    db.cards.clear(),
                    db.decks.clear(),
                    db.media.clear(),
                    db.reviewLog.clear()
                ]);
            });

            await db.setInitialized(false);
            
            window.app.showToast('Todos los datos han sido eliminados', 'success');
            
            // Reload the app
            setTimeout(() => location.reload(), 2000);

        } catch (error) {
            console.error('Error clearing data:', error);
            window.app.showToast('Error al eliminar datos', 'error');
        }
    }

    /**
     * Restart import process
     */
    async restartImport() {
        if (!confirm('¿Quieres reiniciar el proceso de importación? Esto eliminará los datos actuales.')) {
            return;
        }

        try {
            await db.setInitialized(false);
            await db.clearImportCheckpoint();
            
            window.app.showToast('Importación reiniciada', 'success');
            
            // Reload the app to trigger import wizard
            setTimeout(() => location.reload(), 1000);

        } catch (error) {
            console.error('Error restarting import:', error);
            window.app.showToast('Error al reiniciar importación', 'error');
        }
    }

    /**
     * Test SRS system
     */
    testSRS() {
        if (window.srsTest) {
            window.srsTest.runTests();
            window.app.showToast('Pruebas SRS ejecutadas (ver consola)', 'info');
        } else {
            window.app.showToast('Sistema de pruebas SRS no disponible', 'warning');
        }
    }
}

// Register globally
window.SettingsUI = SettingsUI;
