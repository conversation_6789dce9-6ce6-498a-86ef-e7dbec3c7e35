// Database schema and types for CatalanPro
// Uses Dexie.js for IndexedDB management

// TypeScript-style interfaces for documentation
/**
 * @typedef {Object} Card
 * @property {string} id - Unique identifier
 * @property {'term'|'sentence'|'listening'|'conjugation'|'cloze'} type - Card type
 * @property {string} catalan - Catalan text/phrase
 * @property {Object} translations - Translations object
 * @property {string} translations.es - Spanish translation
 * @property {string} translations.en - English translation
 * @property {string} translations.ru - Russian translation
 * @property {string} ipa - IPA pronunciation
 * @property {string[]} syllables - Syllable breakdown
 * @property {string[]} audioIds - Audio file references
 * @property {string[]} examples - Example sentences
 * @property {string[]} grammarTips - Grammar explanations
 * @property {string[]} tags - Categorization tags
 * @property {'A1'|'A2'|'B1'|'B2'|'C1'|'C2'} cefr - CEFR level
 * @property {number} freqRank - Frequency ranking
 * @property {number} ease - SRS ease factor
 * @property {number} interval - Days until next review
 * @property {number} due - Due timestamp
 * @property {number} stability - FSRS stability
 * @property {number} difficulty - FSRS difficulty
 * @property {number} lapses - Number of lapses
 * @property {number} leechCount - Leech threshold counter
 * @property {string} deckId - Parent deck ID
 * @property {number} createdAt - Creation timestamp
 * @property {number} updatedAt - Last update timestamp
 * @property {string} notes - User notes
 */

/**
 * @typedef {Object} ReviewLog
 * @property {string} id - Unique identifier
 * @property {string} cardId - Card reference
 * @property {number} timestamp - Review timestamp
 * @property {1|2|3|4} rating - User rating (Again/Hard/Good/Easy)
 * @property {number} elapsedMs - Time spent reviewing
 * @property {number} prevInterval - Previous interval
 * @property {number} newInterval - New interval
 * @property {number} prevEase - Previous ease
 * @property {number} newEase - New ease
 * @property {'learning'|'review'|'relearning'} state - Card state during review
 */

/**
 * @typedef {Object} Deck
 * @property {string} id - Unique identifier
 * @property {string} title - Deck title
 * @property {string} description - Deck description
 * @property {string[]} tags - Deck tags
 * @property {Object} counts - Card counts by type/level
 */

/**
 * @typedef {Object} Media
 * @property {string} id - Unique identifier
 * @property {string} filename - File path
 * @property {string} checksum - File checksum
 * @property {number} duration - Duration in seconds
 * @property {'audio'} kind - Media type
 * @property {'normal'|'slow'} variant - Speed variant
 */

/**
 * @typedef {Object} Settings
 * @property {string} uiLanguage - UI language code
 * @property {string[]} visibleTranslations - Enabled translation languages
 * @property {number} dailyNewLimit - New cards per day
 * @property {number} dailyReviewCap - Review limit per day
 * @property {'normal'|'slow'} audioSpeedPreference - Audio speed
 * @property {'light'|'dark'|'auto'} theme - UI theme
 * @property {boolean} initialized - Database initialization status
 * @property {Object} importCheckpoint - Import resume data
 */

/**
 * @typedef {Object} Migration
 * @property {string} id - Migration identifier
 * @property {number} fromVersion - Source version
 * @property {number} toVersion - Target version
 * @property {number} appliedAt - Application timestamp
 * @property {string} notes - Migration notes
 */

// Database class
class CatalanProDB extends Dexie {
    constructor() {
        super('CatalanProDB');
        
        // Version 1 - Initial schema
        this.version(1).stores({
            cards: '++id, type, deckId, cefr, due, freqRank, [type+cefr], [deckId+type], [due+type]',
            reviewLog: '++id, cardId, timestamp, [cardId+timestamp]',
            decks: '++id, title',
            media: '++id, filename, kind',
            settings: '++key',
            migrations: '++id, toVersion, appliedAt'
        });

        // Version 2 - Add indexes for better performance
        this.version(2).stores({
            cards: '++id, type, deckId, cefr, due, freqRank, tags, [type+cefr], [deckId+type], [due+type], [cefr+freqRank]',
            reviewLog: '++id, cardId, timestamp, rating, [cardId+timestamp], [timestamp+rating]',
            decks: '++id, title, tags',
            media: '++id, filename, kind, variant, [kind+variant]',
            settings: '++key',
            migrations: '++id, toVersion, appliedAt'
        }).upgrade(tx => {
            // Migration logic for v2
            return tx.migrations.add({
                id: 'v1-to-v2',
                fromVersion: 1,
                toVersion: 2,
                appliedAt: Date.now(),
                notes: 'Added performance indexes and tags support'
            });
        });

        // Define table references
        this.cards = this.table('cards');
        this.reviewLog = this.table('reviewLog');
        this.decks = this.table('decks');
        this.media = this.table('media');
        this.settings = this.table('settings');
        this.migrations = this.table('migrations');
    }

    // Helper methods
    async isInitialized() {
        try {
            const setting = await this.settings.get('initialized');
            return setting?.value === true;
        } catch (error) {
            console.error('Error checking initialization:', error);
            return false;
        }
    }

    async setInitialized(value = true) {
        return this.settings.put({ key: 'initialized', value });
    }

    async getSetting(key, defaultValue = null) {
        try {
            const setting = await this.settings.get(key);
            return setting ? setting.value : defaultValue;
        } catch (error) {
            console.error(`Error getting setting ${key}:`, error);
            return defaultValue;
        }
    }

    async setSetting(key, value) {
        return this.settings.put({ key, value });
    }

    async getImportCheckpoint() {
        return this.getSetting('importCheckpoint', null);
    }

    async setImportCheckpoint(checkpoint) {
        return this.setSetting('importCheckpoint', checkpoint);
    }

    async clearImportCheckpoint() {
        return this.setSetting('importCheckpoint', null);
    }

    // Card queries
    async getDueCards(limit = 100) {
        const now = Date.now();
        return this.cards
            .where('due')
            .belowOrEqual(now)
            .limit(limit)
            .toArray();
    }

    async getNewCards(limit = 20) {
        return this.cards
            .where('interval')
            .equals(0)
            .limit(limit)
            .toArray();
    }

    async getCardsByDeck(deckId) {
        return this.cards
            .where('deckId')
            .equals(deckId)
            .toArray();
    }

    async getCardsByCEFR(level) {
        return this.cards
            .where('cefr')
            .equals(level)
            .toArray();
    }

    async getCardsByType(type) {
        return this.cards
            .where('type')
            .equals(type)
            .toArray();
    }

    // Statistics
    async getCardCounts() {
        const [total, due, newCards] = await Promise.all([
            this.cards.count(),
            this.cards.where('due').belowOrEqual(Date.now()).count(),
            this.cards.where('interval').equals(0).count()
        ]);
        
        return { total, due, new: newCards };
    }

    async getDeckCounts() {
        const decks = await this.decks.toArray();
        const counts = {};
        
        for (const deck of decks) {
            counts[deck.id] = await this.cards.where('deckId').equals(deck.id).count();
        }
        
        return counts;
    }

    // Bulk operations for import
    async bulkAddCards(cards) {
        return this.cards.bulkAdd(cards);
    }

    async bulkAddDecks(decks) {
        return this.decks.bulkAdd(decks);
    }

    async bulkAddMedia(mediaItems) {
        return this.media.bulkAdd(mediaItems);
    }

    // Review logging
    async logReview(cardId, rating, elapsedMs, prevState, newState) {
        const log = {
            cardId,
            timestamp: Date.now(),
            rating,
            elapsedMs,
            prevInterval: prevState.interval,
            newInterval: newState.interval,
            prevEase: prevState.ease,
            newEase: newState.ease,
            state: newState.state || 'review'
        };
        
        return this.reviewLog.add(log);
    }

    // Data integrity
    async validateData() {
        const issues = [];
        
        // Check for cards without decks
        const orphanCards = await this.cards
            .where('deckId')
            .noneOf(await this.decks.toCollection().primaryKeys())
            .count();
        
        if (orphanCards > 0) {
            issues.push(`${orphanCards} cards reference non-existent decks`);
        }
        
        // Check for missing audio files
        const cardsWithAudio = await this.cards
            .filter(card => card.audioIds && card.audioIds.length > 0)
            .toArray();
        
        const mediaIds = new Set(await this.media.toCollection().primaryKeys());
        let missingAudio = 0;
        
        for (const card of cardsWithAudio) {
            for (const audioId of card.audioIds) {
                if (!mediaIds.has(audioId)) {
                    missingAudio++;
                    break;
                }
            }
        }
        
        if (missingAudio > 0) {
            issues.push(`${missingAudio} cards reference missing audio files`);
        }
        
        return {
            valid: issues.length === 0,
            issues,
            cardCount: await this.cards.count(),
            deckCount: await this.decks.count(),
            mediaCount: await this.media.count()
        };
    }

    // Backup and restore
    async exportData() {
        const [cards, decks, media, settings, reviewLog] = await Promise.all([
            this.cards.toArray(),
            this.decks.toArray(),
            this.media.toArray(),
            this.settings.toArray(),
            this.reviewLog.toArray()
        ]);
        
        return {
            version: this.verno,
            exportedAt: Date.now(),
            cards,
            decks,
            media,
            settings,
            reviewLog
        };
    }

    async importData(data) {
        await this.transaction('rw', this.cards, this.decks, this.media, this.settings, this.reviewLog, async () => {
            // Clear existing data
            await Promise.all([
                this.cards.clear(),
                this.decks.clear(),
                this.media.clear(),
                this.settings.clear(),
                this.reviewLog.clear()
            ]);
            
            // Import new data
            await Promise.all([
                this.cards.bulkAdd(data.cards),
                this.decks.bulkAdd(data.decks),
                this.media.bulkAdd(data.media),
                this.settings.bulkAdd(data.settings),
                this.reviewLog.bulkAdd(data.reviewLog)
            ]);
        });
    }
}

// Create global database instance
const db = new CatalanProDB();

// Export for use in other modules
window.db = db;
