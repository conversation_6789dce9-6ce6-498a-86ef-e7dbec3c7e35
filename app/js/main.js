// Main application bootstrap and initialization
// Handles app startup, import wizard, and view management

class CatalanProApp {
    constructor() {
        this.currentView = 'dashboard';
        this.isInitialized = false;
        this.ui = {};
        
        // Initialize UI modules when they're loaded
        this.initializeUI();
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            console.log('🚀 Initializing CatalanPro...');
            
            // Show loading screen
            this.showLoading('Inicializando aplicación...');
            
            // Initialize database
            await this.initializeDatabase();
            
            // Check if app is initialized
            const initialized = await db.isInitialized();
            
            if (!initialized) {
                // Show import wizard
                await this.showImportWizard();
            } else {
                // Go directly to app
                await this.showApp();
            }
            
        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.showError('Error al inicializar la aplicación: ' + error.message);
        }
    }

    /**
     * Initialize database connection
     */
    async initializeDatabase() {
        try {
            await db.open();
            console.log('✅ Database connected');
            
            // Initialize default settings
            await this.initializeDefaultSettings();
            
        } catch (error) {
            console.error('Database initialization failed:', error);
            throw new Error('No se pudo conectar a la base de datos');
        }
    }

    /**
     * Initialize default settings
     */
    async initializeDefaultSettings() {
        const defaults = {
            uiLanguage: 'ca',
            visibleTranslations: ['es', 'en'],
            dailyNewLimit: 20,
            dailyReviewCap: 100,
            audioSpeedPreference: 'normal',
            theme: 'light'
        };

        for (const [key, value] of Object.entries(defaults)) {
            const existing = await db.getSetting(key);
            if (existing === null) {
                await db.setSetting(key, value);
            }
        }
    }

    /**
     * Show import wizard
     */
    async showImportWizard() {
        this.hideLoading();
        
        const wizard = document.getElementById('import-wizard');
        wizard.classList.remove('hidden');
        
        // Set up import wizard event handlers
        this.setupImportWizardHandlers();
        
        // Auto-start import
        setTimeout(() => this.startImport(), 1000);
    }

    /**
     * Set up import wizard event handlers
     */
    setupImportWizardHandlers() {
        const pauseBtn = document.getElementById('pause-import');
        const resumeBtn = document.getElementById('resume-import');
        const skipBtn = document.getElementById('skip-import');

        pauseBtn.addEventListener('click', () => {
            importer.pause();
            pauseBtn.classList.add('hidden');
            resumeBtn.classList.remove('hidden');
        });

        resumeBtn.addEventListener('click', () => {
            importer.resume();
            resumeBtn.classList.add('hidden');
            pauseBtn.classList.remove('hidden');
        });

        skipBtn.addEventListener('click', () => {
            this.skipImport();
        });
    }

    /**
     * Start the import process
     */
    async startImport() {
        try {
            await importer.startImport(
                (progress) => this.updateImportProgress(progress),
                (message) => this.addImportLog(message)
            );
            
            // Import completed successfully
            this.addImportLog('🎉 ¡Importación completada!');
            
            setTimeout(() => {
                this.hideImportWizard();
                this.showApp();
            }, 2000);
            
        } catch (error) {
            console.error('Import failed:', error);
            this.addImportLog(`❌ Error: ${error.message}`);
            
            // Show retry option
            this.showImportError(error);
        }
    }

    /**
     * Skip import and use demo mode
     */
    async skipImport() {
        try {
            this.addImportLog('Configurando modo demo...');
            
            // Create minimal demo data
            await this.createDemoData();
            
            // Mark as initialized
            await db.setInitialized(true);
            
            this.hideImportWizard();
            this.showApp();
            
        } catch (error) {
            console.error('Demo setup failed:', error);
            this.addImportLog(`❌ Error configurando demo: ${error.message}`);
        }
    }

    /**
     * Create minimal demo data
     */
    async createDemoData() {
        const demoDeck = {
            id: 'demo-deck',
            title: 'Demo - Vocabulario básico',
            description: 'Palabras básicas para empezar',
            tags: ['demo', 'básico'],
            counts: { total: 5, new: 5, due: 0 }
        };

        const demoCards = [
            {
                id: 'demo-1',
                type: 'term',
                catalan: 'hola',
                translations: { es: 'hola', en: 'hello', ru: 'привет' },
                ipa: 'ˈo.lə',
                syllables: ['ho', 'la'],
                audioIds: [],
                examples: ['Hola, com estàs?'],
                grammarTips: ['Saludo informal'],
                tags: ['saludo', 'básico'],
                cefr: 'A1',
                freqRank: 1,
                ease: 2.5,
                interval: 0,
                due: 0,
                stability: 4.0,
                difficulty: 4.0,
                lapses: 0,
                leechCount: 0,
                deckId: 'demo-deck',
                createdAt: Date.now(),
                updatedAt: Date.now(),
                notes: ''
            },
            {
                id: 'demo-2',
                type: 'term',
                catalan: 'gràcies',
                translations: { es: 'gracias', en: 'thank you', ru: 'спасибо' },
                ipa: 'ˈɡɾa.si.əs',
                syllables: ['grà', 'ci', 'es'],
                audioIds: [],
                examples: ['Gràcies per la teva ajuda.'],
                grammarTips: ['Expresión de agradecimiento'],
                tags: ['cortesía', 'básico'],
                cefr: 'A1',
                freqRank: 2,
                ease: 2.5,
                interval: 0,
                due: 0,
                stability: 4.0,
                difficulty: 4.0,
                lapses: 0,
                leechCount: 0,
                deckId: 'demo-deck',
                createdAt: Date.now(),
                updatedAt: Date.now(),
                notes: ''
            }
        ];

        await db.transaction('rw', db.decks, db.cards, async () => {
            await db.decks.add(demoDeck);
            await db.cards.bulkAdd(demoCards);
        });
    }

    /**
     * Update import progress display
     */
    updateImportProgress(progress) {
        const currentFileEl = document.getElementById('current-file');
        const fileProgressEl = document.getElementById('file-progress');
        const fileProgressTextEl = document.getElementById('file-progress-text');
        const overallProgressEl = document.getElementById('overall-progress');
        const overallProgressTextEl = document.getElementById('overall-progress-text');

        if (currentFileEl) currentFileEl.textContent = progress.currentFile;
        if (fileProgressEl) fileProgressEl.style.width = `${progress.fileProgress}%`;
        if (fileProgressTextEl) fileProgressTextEl.textContent = `${Math.round(progress.fileProgress)}%`;
        if (overallProgressEl) overallProgressEl.style.width = `${progress.overallProgress}%`;
        if (overallProgressTextEl) overallProgressTextEl.textContent = `${Math.round(progress.overallProgress)}%`;
    }

    /**
     * Add message to import log
     */
    addImportLog(message) {
        const logEl = document.getElementById('import-log');
        if (logEl) {
            const timestamp = new Date().toLocaleTimeString();
            logEl.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logEl.scrollTop = logEl.scrollHeight;
        }
    }

    /**
     * Show import error and retry options
     */
    showImportError(error) {
        const actionsEl = document.querySelector('.import-actions');
        if (actionsEl) {
            actionsEl.innerHTML = `
                <button id="retry-import" class="btn btn-primary">Reintentar</button>
                <button id="skip-import-error" class="btn btn-secondary">Modo demo</button>
            `;

            document.getElementById('retry-import').addEventListener('click', () => {
                location.reload(); // Simple retry by reloading
            });

            document.getElementById('skip-import-error').addEventListener('click', () => {
                this.skipImport();
            });
        }
    }

    /**
     * Hide import wizard and show main app
     */
    hideImportWizard() {
        const wizard = document.getElementById('import-wizard');
        wizard.classList.add('hidden');
    }

    /**
     * Show main application
     */
    async showApp() {
        this.hideLoading();
        
        const app = document.getElementById('app');
        app.classList.remove('hidden');
        
        // Initialize UI modules
        await this.initializeUIModules();
        
        // Set up navigation
        this.setupNavigation();
        
        // Load initial view
        await this.showView('dashboard');
        
        this.isInitialized = true;
        console.log('✅ CatalanPro initialized successfully');
    }

    /**
     * Initialize UI modules
     */
    async initializeUIModules() {
        // Initialize each UI module if available
        if (window.DashboardUI) {
            this.ui.dashboard = new DashboardUI();
            await this.ui.dashboard.init();
        }
        
        if (window.ReviewUI) {
            this.ui.review = new ReviewUI();
            await this.ui.review.init();
        }
        
        if (window.EditorUI) {
            this.ui.editor = new EditorUI();
            await this.ui.editor.init();
        }
        
        if (window.AnalyticsUI) {
            this.ui.analytics = new AnalyticsUI();
            await this.ui.analytics.init();
        }
        
        if (window.SettingsUI) {
            this.ui.settings = new SettingsUI();
            await this.ui.settings.init();
        }
    }

    /**
     * Initialize UI module references
     */
    initializeUI() {
        // This will be called when UI modules are loaded
        // UI modules will register themselves globally
    }

    /**
     * Set up navigation event handlers
     */
    setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const view = item.dataset.view;
                if (view) {
                    this.showView(view);
                }
            });
        });
    }

    /**
     * Show a specific view
     */
    async showView(viewName) {
        // Hide all views
        document.querySelectorAll('.view').forEach(view => {
            view.classList.remove('active');
        });
        
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Show target view
        const targetView = document.getElementById(`${viewName}-view`);
        if (targetView) {
            targetView.classList.add('active');
        }
        
        // Update navigation
        const targetNav = document.querySelector(`[data-view="${viewName}"]`);
        if (targetNav) {
            targetNav.classList.add('active');
        }
        
        // Initialize view if needed
        if (this.ui[viewName] && this.ui[viewName].show) {
            await this.ui[viewName].show();
        }
        
        this.currentView = viewName;
    }

    /**
     * Show loading screen
     */
    showLoading(message = 'Cargando...') {
        const loadingScreen = document.getElementById('loading-screen');
        const loadingText = document.getElementById('loading-text');
        
        if (loadingText) loadingText.textContent = message;
        if (loadingScreen) loadingScreen.classList.remove('hidden');
    }

    /**
     * Hide loading screen
     */
    hideLoading() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) loadingScreen.classList.add('hidden');
    }

    /**
     * Show error message
     */
    showError(message) {
        this.hideLoading();
        alert(message); // TODO: Replace with proper error UI
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        const container = document.getElementById('toast-container');
        if (!container) return;

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        container.appendChild(toast);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }
}

// Initialize app when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.app = new CatalanProApp();
    window.app.init();
});

// Register service worker for PWA
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}
