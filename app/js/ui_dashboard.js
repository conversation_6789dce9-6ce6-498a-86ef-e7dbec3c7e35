// Dashboard UI Module
// Handles the main dashboard view with statistics and deck overview

class DashboardUI {
    constructor() {
        this.refreshInterval = null;
        this.stats = {
            due: 0,
            new: 0,
            streak: 0,
            total: 0
        };
    }

    /**
     * Initialize the dashboard
     */
    async init() {
        console.log('Initializing Dashboard UI');
        this.setupEventHandlers();
        await this.loadStats();
        await this.loadDecks();
        
        // Auto-refresh every 30 seconds
        this.refreshInterval = setInterval(() => {
            this.loadStats();
        }, 30000);
    }

    /**
     * Set up event handlers
     */
    setupEventHandlers() {
        const startReviewBtn = document.getElementById('start-review');
        const studyNewBtn = document.getElementById('study-new');

        if (startReviewBtn) {
            startReviewBtn.addEventListener('click', () => {
                this.startReview();
            });
        }

        if (studyNewBtn) {
            studyNewBtn.addEventListener('click', () => {
                this.studyNew();
            });
        }
    }

    /**
     * Show the dashboard (called when view becomes active)
     */
    async show() {
        await this.loadStats();
        await this.loadDecks();
        this.updateGreeting();
    }

    /**
     * Load and display statistics
     */
    async loadStats() {
        try {
            const cardCounts = await db.getCardCounts();
            const streak = await this.calculateStreak();
            
            this.stats = {
                due: cardCounts.due,
                new: cardCounts.new,
                streak: streak,
                total: cardCounts.total
            };

            this.updateStatsDisplay();
            this.updateActionButtons();

        } catch (error) {
            console.error('Error loading stats:', error);
        }
    }

    /**
     * Update statistics display
     */
    updateStatsDisplay() {
        const elements = {
            'due-count': this.stats.due,
            'new-count': this.stats.new,
            'streak-count': this.stats.streak,
            'total-cards': this.stats.total
        };

        for (const [id, value] of Object.entries(elements)) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value.toLocaleString();
            }
        }
    }

    /**
     * Update action button states
     */
    updateActionButtons() {
        const startReviewBtn = document.getElementById('start-review');
        const studyNewBtn = document.getElementById('study-new');

        if (startReviewBtn) {
            if (this.stats.due > 0) {
                startReviewBtn.disabled = false;
                startReviewBtn.innerHTML = `
                    <span class="btn-icon">🎯</span>
                    Repasar (${this.stats.due})
                `;
            } else {
                startReviewBtn.disabled = true;
                startReviewBtn.innerHTML = `
                    <span class="btn-icon">✅</span>
                    Todo al día
                `;
            }
        }

        if (studyNewBtn) {
            if (this.stats.new > 0) {
                studyNewBtn.disabled = false;
                studyNewBtn.innerHTML = `
                    <span class="btn-icon">🆕</span>
                    Estudiar nuevas (${this.stats.new})
                `;
            } else {
                studyNewBtn.disabled = true;
                studyNewBtn.innerHTML = `
                    <span class="btn-icon">📚</span>
                    Sin nuevas
                `;
            }
        }
    }

    /**
     * Calculate current study streak
     */
    async calculateStreak() {
        try {
            // Get review logs from the last 30 days
            const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
            const recentReviews = await db.reviewLog
                .where('timestamp')
                .above(thirtyDaysAgo)
                .toArray();

            if (recentReviews.length === 0) return 0;

            // Group reviews by day
            const reviewsByDay = {};
            for (const review of recentReviews) {
                const day = new Date(review.timestamp).toDateString();
                reviewsByDay[day] = true;
            }

            // Calculate consecutive days from today backwards
            let streak = 0;
            const today = new Date();
            
            for (let i = 0; i < 30; i++) {
                const checkDate = new Date(today);
                checkDate.setDate(today.getDate() - i);
                const dayString = checkDate.toDateString();
                
                if (reviewsByDay[dayString]) {
                    streak++;
                } else {
                    break;
                }
            }

            return streak;

        } catch (error) {
            console.error('Error calculating streak:', error);
            return 0;
        }
    }

    /**
     * Load and display available decks
     */
    async loadDecks() {
        try {
            const decks = await db.decks.toArray();
            const deckCounts = await db.getDeckCounts();
            
            const deckListEl = document.getElementById('deck-list');
            if (!deckListEl) return;

            if (decks.length === 0) {
                deckListEl.innerHTML = `
                    <div class="empty-state">
                        <p>No hay mazos disponibles</p>
                        <p>Importa datos o crea mazos personalizados</p>
                    </div>
                `;
                return;
            }

            deckListEl.innerHTML = decks.map(deck => {
                const cardCount = deckCounts[deck.id] || 0;
                return this.renderDeckCard(deck, cardCount);
            }).join('');

            // Add click handlers for deck cards
            deckListEl.querySelectorAll('.deck-card').forEach(card => {
                card.addEventListener('click', (e) => {
                    const deckId = card.dataset.deckId;
                    this.openDeck(deckId);
                });
            });

        } catch (error) {
            console.error('Error loading decks:', error);
        }
    }

    /**
     * Render a deck card
     */
    renderDeckCard(deck, cardCount) {
        const tags = deck.tags ? deck.tags.slice(0, 3).map(tag => 
            `<span class="deck-tag">${tag}</span>`
        ).join('') : '';

        return `
            <div class="deck-card" data-deck-id="${deck.id}">
                <div class="deck-header">
                    <h3 class="deck-title">${this.escapeHtml(deck.title)}</h3>
                    <div class="deck-tags">${tags}</div>
                </div>
                <p class="deck-description">${this.escapeHtml(deck.description || '')}</p>
                <div class="deck-stats">
                    <span class="deck-stat">
                        <span class="stat-icon">📚</span>
                        ${cardCount} tarjetas
                    </span>
                    <span class="deck-stat">
                        <span class="stat-icon">🎯</span>
                        ${deck.counts?.due || 0} pendientes
                    </span>
                </div>
            </div>
        `;
    }

    /**
     * Update greeting based on time of day
     */
    updateGreeting() {
        const header = document.querySelector('.dashboard-header h1');
        if (!header) return;

        const hour = new Date().getHours();
        let greeting;

        if (hour < 12) {
            greeting = 'Bon dia! 🌅';
        } else if (hour < 18) {
            greeting = 'Bona tarda! ☀️';
        } else {
            greeting = 'Bon vespre! 🌙';
        }

        header.textContent = greeting;
    }

    /**
     * Start review session
     */
    async startReview() {
        if (this.stats.due === 0) {
            window.app.showToast('No hay tarjetas para repasar', 'info');
            return;
        }

        try {
            // Switch to review view
            await window.app.showView('review');
            
            // Start review session with due cards
            if (window.app.ui.review) {
                await window.app.ui.review.startSession('review');
            }

        } catch (error) {
            console.error('Error starting review:', error);
            window.app.showToast('Error al iniciar el repaso', 'error');
        }
    }

    /**
     * Start new card study session
     */
    async studyNew() {
        if (this.stats.new === 0) {
            window.app.showToast('No hay tarjetas nuevas disponibles', 'info');
            return;
        }

        try {
            // Switch to review view
            await window.app.showView('review');
            
            // Start review session with new cards
            if (window.app.ui.review) {
                await window.app.ui.review.startSession('new');
            }

        } catch (error) {
            console.error('Error starting new study:', error);
            window.app.showToast('Error al estudiar nuevas tarjetas', 'error');
        }
    }

    /**
     * Open a specific deck
     */
    async openDeck(deckId) {
        try {
            // Switch to review view with deck filter
            await window.app.showView('review');
            
            if (window.app.ui.review) {
                await window.app.ui.review.startSession('deck', { deckId });
            }

        } catch (error) {
            console.error('Error opening deck:', error);
            window.app.showToast('Error al abrir el mazo', 'error');
        }
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Cleanup when dashboard is destroyed
     */
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
}

// Register globally
window.DashboardUI = DashboardUI;
