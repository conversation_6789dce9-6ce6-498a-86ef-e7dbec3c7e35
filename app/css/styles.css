/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary: #d32f2f;
    --primary-dark: #b71c1c;
    --primary-light: #ffcdd2;
    --secondary: #1976d2;
    --secondary-dark: #0d47a1;
    --secondary-light: #bbdefb;
    --success: #388e3c;
    --warning: #f57c00;
    --error: #d32f2f;
    --background: #fafafa;
    --surface: #ffffff;
    --surface-variant: #f5f5f5;
    --on-surface: #212121;
    --on-surface-variant: #757575;
    --border: #e0e0e0;
    --shadow: rgba(0, 0, 0, 0.1);
    --radius: 8px;
    --radius-large: 16px;
    --spacing: 8px;
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

body {
    font-family: var(--font-family);
    background: var(--background);
    color: var(--on-surface);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    color: white;
}

.loading-content {
    text-align: center;
    animation: fadeIn 0.5s ease-in;
}

.logo {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 1rem auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Utility Classes */
.hidden { display: none !important; }
.sr-only { position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0; }

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: var(--spacing);
}

.modal-content {
    background: var(--surface);
    border-radius: var(--radius-large);
    padding: 2rem;
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 8px 32px var(--shadow);
}

/* Import Wizard */
.import-progress {
    margin: 2rem 0;
}

.file-progress, .overall-progress {
    margin-bottom: 1.5rem;
}

.file-name {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary);
}

.progress-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.progress-bar {
    height: 8px;
    background: var(--border);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: var(--primary);
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-text {
    text-align: right;
    font-size: 0.875rem;
    color: var(--on-surface-variant);
}

.import-log {
    background: var(--surface-variant);
    border-radius: var(--radius);
    padding: 1rem;
    max-height: 200px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 0.875rem;
    margin: 1rem 0;
}

.import-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    background: transparent;
}

.btn:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

.btn-primary {
    background: var(--primary);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-secondary {
    background: var(--secondary);
    color: white;
}

.btn-secondary:hover {
    background: var(--secondary-dark);
}

.btn-outline {
    border: 1px solid var(--border);
    color: var(--on-surface);
}

.btn-outline:hover {
    background: var(--surface-variant);
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1rem;
}

.btn-icon {
    font-size: 1.2em;
}

/* App Layout */
.app {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Navigation */
.navbar {
    background: var(--surface);
    border-bottom: 1px solid var(--border);
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 1.25rem;
}

.nav-menu {
    display: flex;
    gap: 0.5rem;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem;
    border: none;
    background: transparent;
    border-radius: var(--radius);
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--on-surface-variant);
    min-width: 60px;
}

.nav-item:hover {
    background: var(--surface-variant);
    color: var(--on-surface);
}

.nav-item.active {
    background: var(--primary-light);
    color: var(--primary);
}

.nav-icon {
    font-size: 1.25rem;
}

.nav-text {
    font-size: 0.75rem;
    font-weight: 500;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

/* Views */
.view {
    display: none;
    animation: fadeIn 0.3s ease-in;
}

.view.active {
    display: block;
}

/* Dashboard */
.dashboard-header {
    text-align: center;
    margin-bottom: 2rem;
}

.dashboard-subtitle {
    color: var(--on-surface-variant);
    font-size: 1.125rem;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--surface);
    border-radius: var(--radius-large);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 2px 8px var(--shadow);
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 2rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary);
}

.stat-label {
    color: var(--on-surface-variant);
    font-size: 0.875rem;
}

.dashboard-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 3rem;
}

.deck-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
}

.deck-card {
    background: var(--surface);
    border-radius: var(--radius-large);
    padding: 1.5rem;
    box-shadow: 0 2px 8px var(--shadow);
    transition: transform 0.2s ease;
    cursor: pointer;
}

.deck-card:hover {
    transform: translateY(-2px);
}

.deck-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.deck-description {
    color: var(--on-surface-variant);
    margin-bottom: 1rem;
}

.deck-stats {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
    color: var(--on-surface-variant);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 2000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.toast {
    background: var(--surface);
    border-radius: var(--radius);
    padding: 1rem;
    box-shadow: 0 4px 16px var(--shadow);
    border-left: 4px solid var(--primary);
    animation: slideIn 0.3s ease-out;
    max-width: 300px;
}

.toast.success { border-left-color: var(--success); }
.toast.warning { border-left-color: var(--warning); }
.toast.error { border-left-color: var(--error); }

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Review Interface */
.review-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 1rem;
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--surface);
    border-radius: var(--radius);
    box-shadow: 0 2px 8px var(--shadow);
}

.session-info {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.session-type {
    font-weight: 600;
    color: var(--primary);
}

.progress {
    font-size: 0.875rem;
    color: var(--on-surface-variant);
}

.review-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.mode-selector {
    padding: 0.5rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--surface);
}

.card-container {
    margin-bottom: 2rem;
}

.card-content {
    background: var(--surface);
    border-radius: var(--radius-large);
    padding: 2rem;
    min-height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    box-shadow: 0 4px 16px var(--shadow);
}

.card-text-main {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--on-surface);
}

.card-ipa {
    font-size: 1.25rem;
    color: var(--on-surface-variant);
    font-family: 'Lucida Sans Unicode', sans-serif;
    margin-bottom: 1rem;
}

.card-answer {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border);
    width: 100%;
}

.translations {
    margin-bottom: 1rem;
}

.translation {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    justify-content: center;
}

.lang-code {
    font-weight: 600;
    color: var(--primary);
    min-width: 30px;
}

.translation-text {
    color: var(--on-surface);
}

.examples, .grammar-tips {
    margin-top: 1rem;
    text-align: left;
}

.example, .grammar-tip {
    margin-bottom: 0.5rem;
    font-style: italic;
    color: var(--on-surface-variant);
}

.audio-controls {
    margin: 1rem 0;
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.audio-controls.prominent {
    margin: 2rem 0;
}

.audio-btn, .audio-speed-btn {
    padding: 0.75rem 1.5rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--surface);
    cursor: pointer;
    transition: all 0.2s ease;
}

.audio-btn:hover, .audio-speed-btn:hover {
    background: var(--surface-variant);
}

.review-actions {
    margin-bottom: 2rem;
}

.answer-section, .question-section {
    display: flex;
    justify-content: center;
}

.rating-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.rating-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    border: 2px solid transparent;
    border-radius: var(--radius);
    background: var(--surface);
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
}

.rating-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow);
}

.rating-btn.again {
    border-color: var(--error);
    color: var(--error);
}

.rating-btn.again:hover {
    background: var(--error);
    color: white;
}

.rating-btn.hard {
    border-color: var(--warning);
    color: var(--warning);
}

.rating-btn.hard:hover {
    background: var(--warning);
    color: white;
}

.rating-btn.good {
    border-color: var(--success);
    color: var(--success);
}

.rating-btn.good:hover {
    background: var(--success);
    color: white;
}

.rating-btn.easy {
    border-color: var(--secondary);
    color: var(--secondary);
}

.rating-btn.easy:hover {
    background: var(--secondary);
    color: white;
}

.rating-label {
    font-weight: 500;
}

.rating-key {
    font-size: 0.75rem;
    opacity: 0.7;
}

.shortcut {
    font-size: 0.75rem;
    opacity: 0.7;
    margin-left: 0.5rem;
}

.session-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    padding: 1rem;
    background: var(--surface-variant);
    border-radius: var(--radius);
}

.stat {
    text-align: center;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--on-surface-variant);
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary);
}

/* Editor Interface */
.editor-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.editor-sidebar {
    background: var(--surface);
    border-radius: var(--radius-large);
    padding: 1.5rem;
    height: fit-content;
    box-shadow: 0 2px 8px var(--shadow);
}

.search-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    margin-bottom: 1rem;
}

.search-results {
    max-height: 300px;
    overflow-y: auto;
}

.card-search-result {
    padding: 0.75rem;
    border-bottom: 1px solid var(--border);
    cursor: pointer;
    transition: background 0.2s ease;
}

.card-search-result:hover {
    background: var(--surface-variant);
}

.result-main {
    font-weight: 500;
}

.result-translation {
    font-size: 0.875rem;
    color: var(--on-surface-variant);
}

.editor-main {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
}

.card-form {
    background: var(--surface);
    border-radius: var(--radius-large);
    padding: 2rem;
    box-shadow: 0 2px 8px var(--shadow);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.875rem;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(211, 47, 47, 0.1);
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--on-surface);
}

.translations-group {
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.translations-group h3 {
    margin-bottom: 1rem;
    color: var(--primary);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border);
}

.card-preview {
    background: var(--surface);
    border-radius: var(--radius-large);
    padding: 1.5rem;
    box-shadow: 0 2px 8px var(--shadow);
    height: fit-content;
}

.preview-content {
    margin-top: 1rem;
}

.preview-card {
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 1rem;
}

.preview-question {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border);
}

.preview-answer {
    margin-bottom: 1rem;
}

.preview-translation {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
}

.preview-meta {
    font-size: 0.75rem;
    color: var(--on-surface-variant);
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.meta-item {
    background: var(--surface-variant);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

/* Analytics Interface */
.analytics-container {
    max-width: 1200px;
    margin: 0 auto;
}

.analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.analytics-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.analytics-card {
    background: var(--surface);
    border-radius: var(--radius-large);
    padding: 1.5rem;
    box-shadow: 0 2px 8px var(--shadow);
}

.analytics-card h2 {
    margin-bottom: 1rem;
    color: var(--primary);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: var(--surface-variant);
    border-radius: var(--radius);
}

.chart-container {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-placeholder {
    color: var(--on-surface-variant);
    font-style: italic;
}

.simple-chart {
    display: flex;
    align-items: end;
    gap: 4px;
    height: 150px;
    padding: 1rem 0;
}

.chart-bar {
    flex: 1;
    background: var(--primary);
    border-radius: 2px 2px 0 0;
    min-height: 4px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.bar-value {
    font-size: 0.75rem;
    color: white;
    padding: 2px;
}

.bar-label {
    font-size: 0.75rem;
    color: var(--on-surface-variant);
    margin-top: 4px;
}

.cefr-item, .type-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.cefr-level, .type-label {
    min-width: 30px;
    font-weight: 500;
}

.cefr-bar, .type-bar {
    flex: 1;
    height: 20px;
    background: var(--surface-variant);
    border-radius: 10px;
    overflow: hidden;
}

.cefr-fill, .type-fill {
    height: 100%;
    background: var(--primary);
    transition: width 0.3s ease;
}

.cefr-text, .type-text {
    font-size: 0.875rem;
    color: var(--on-surface-variant);
    min-width: 80px;
    text-align: right;
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid var(--border);
}

.activity-time {
    font-weight: 500;
}

.activity-stats {
    color: var(--on-surface-variant);
    font-size: 0.875rem;
}

.activity-duration {
    font-size: 0.875rem;
    color: var(--primary);
}

.progress-metrics {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.progress-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.progress-label {
    font-weight: 500;
}

.level-indicator {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
    text-align: center;
    padding: 0.5rem;
    background: var(--primary-light);
    border-radius: var(--radius);
}

.analytics-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Settings Interface */
.settings-container {
    max-width: 800px;
    margin: 0 auto;
}

.settings-section {
    background: var(--surface);
    border-radius: var(--radius-large);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px var(--shadow);
}

.settings-section h2 {
    margin-bottom: 1.5rem;
    color: var(--primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.setting-item {
    margin-bottom: 1.5rem;
}

.setting-item:last-child {
    margin-bottom: 0;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

.setting-help {
    font-size: 0.875rem;
    color: var(--on-surface-variant);
    margin-top: 0.25rem;
}

.data-info, .app-info {
    background: var(--surface-variant);
    border-radius: var(--radius);
    padding: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    font-weight: 500;
}

.info-value {
    color: var(--on-surface-variant);
}

.setting-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Responsive */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }

    .dashboard-actions {
        flex-direction: column;
        align-items: center;
    }

    .nav-text {
        display: none;
    }

    .nav-item {
        min-width: 48px;
    }

    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .editor-container {
        grid-template-columns: 1fr;
    }

    .editor-main {
        grid-template-columns: 1fr;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .rating-buttons {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .rating-btn {
        min-width: 60px;
        padding: 0.75rem 1rem;
    }

    .card-text-main {
        font-size: 1.5rem;
    }

    .review-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .session-stats {
        flex-direction: column;
        gap: 1rem;
    }
}
