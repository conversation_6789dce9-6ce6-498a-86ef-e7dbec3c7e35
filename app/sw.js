// Service Worker for CatalanPro PWA
// Provides offline functionality and caching strategies

const CACHE_NAME = 'catalanpro-v1.0.0';
const STATIC_CACHE = 'catalanpro-static-v1.0.0';
const DATA_CACHE = 'catalanpro-data-v1.0.0';

// Files to cache for offline functionality
const STATIC_FILES = [
  '/',
  '/index.html',
  '/css/styles.css',
  '/js/db.js',
  '/js/srs.js',
  '/js/importer.js',
  '/js/ui_dashboard.js',
  '/js/ui_review.js',
  '/js/ui_editor.js',
  '/js/ui_analytics.js',
  '/js/ui_settings.js',
  '/js/main.js',
  '/manifest.webmanifest',
  'https://unpkg.com/dexie@3.2.4/dist/dexie.js'
];

// Data files that should be cached with cache-first strategy
const DATA_FILES = [
  '/data/',
  '/media/'
];

/**
 * Install event - cache static files
 */
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('[SW] Caching static files');
        return cache.addAll(STATIC_FILES);
      })
      .then(() => {
        console.log('[SW] Static files cached successfully');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('[SW] Failed to cache static files:', error);
      })
  );
});

/**
 * Activate event - clean up old caches
 */
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DATA_CACHE) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[SW] Service worker activated');
        return self.clients.claim();
      })
  );
});

/**
 * Fetch event - handle network requests with appropriate caching strategy
 */
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip chrome-extension and other non-http(s) requests
  if (!url.protocol.startsWith('http')) {
    return;
  }

  // Handle different types of requests
  if (isStaticFile(request.url)) {
    // Static files: stale-while-revalidate
    event.respondWith(staleWhileRevalidate(request, STATIC_CACHE));
  } else if (isDataFile(request.url)) {
    // Data files: cache-first with long TTL
    event.respondWith(cacheFirst(request, DATA_CACHE));
  } else if (isExternalLibrary(request.url)) {
    // External libraries: cache-first
    event.respondWith(cacheFirst(request, STATIC_CACHE));
  } else {
    // Other requests: network-first with fallback
    event.respondWith(networkFirst(request));
  }
});

/**
 * Check if URL is a static file
 */
function isStaticFile(url) {
  return STATIC_FILES.some(file => url.endsWith(file)) ||
         url.includes('/css/') ||
         url.includes('/js/') ||
         url.endsWith('.html') ||
         url.endsWith('.webmanifest');
}

/**
 * Check if URL is a data file
 */
function isDataFile(url) {
  return url.includes('/data/') || 
         url.includes('/media/') ||
         url.endsWith('.jsonl') ||
         url.endsWith('.mp3') ||
         url.endsWith('.wav');
}

/**
 * Check if URL is an external library
 */
function isExternalLibrary(url) {
  return url.includes('unpkg.com') ||
         url.includes('cdn.') ||
         url.includes('jsdelivr.net');
}

/**
 * Stale-while-revalidate caching strategy
 * Serves from cache immediately, then updates cache in background
 */
async function staleWhileRevalidate(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  // Fetch from network in background to update cache
  const networkPromise = fetch(request)
    .then((response) => {
      if (response.ok) {
        cache.put(request, response.clone());
      }
      return response;
    })
    .catch((error) => {
      console.log('[SW] Network fetch failed:', error);
      return null;
    });
  
  // Return cached version immediately if available
  if (cachedResponse) {
    // Update cache in background
    networkPromise;
    return cachedResponse;
  }
  
  // If no cache, wait for network
  const networkResponse = await networkPromise;
  if (networkResponse) {
    return networkResponse;
  }
  
  // Fallback for critical files
  if (request.url.endsWith('/') || request.url.endsWith('index.html')) {
    return cache.match('/index.html');
  }
  
  return new Response('Offline - Content not available', {
    status: 503,
    statusText: 'Service Unavailable'
  });
}

/**
 * Cache-first strategy
 * Serves from cache if available, otherwise fetches from network
 */
async function cacheFirst(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.log('[SW] Cache-first fetch failed:', error);
    return new Response('Offline - Content not available', {
      status: 503,
      statusText: 'Service Unavailable'
    });
  }
}

/**
 * Network-first strategy
 * Tries network first, falls back to cache
 */
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    
    // Cache successful responses
    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('[SW] Network-first fetch failed, trying cache:', error);
    
    // Try to serve from cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Ultimate fallback
    return new Response('Offline - Content not available', {
      status: 503,
      statusText: 'Service Unavailable'
    });
  }
}

/**
 * Background sync for data updates
 */
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync triggered:', event.tag);
  
  if (event.tag === 'background-sync-data') {
    event.waitUntil(syncData());
  }
});

/**
 * Sync data in background
 */
async function syncData() {
  try {
    console.log('[SW] Syncing data in background');
    
    // Check for data updates
    const dataFiles = [
      '/data/decks_topics.jsonl',
      '/data/media_manifest.jsonl'
    ];
    
    const cache = await caches.open(DATA_CACHE);
    
    for (const file of dataFiles) {
      try {
        const response = await fetch(file);
        if (response.ok) {
          await cache.put(file, response);
          console.log('[SW] Updated cache for:', file);
        }
      } catch (error) {
        console.log('[SW] Failed to update:', file, error);
      }
    }
    
  } catch (error) {
    console.error('[SW] Background sync failed:', error);
  }
}

/**
 * Handle push notifications (for future use)
 */
self.addEventListener('push', (event) => {
  console.log('[SW] Push notification received');
  
  const options = {
    body: 'És hora de repassar les teves targetes!',
    icon: '/icon-192.png',
    badge: '/badge-72.png',
    tag: 'study-reminder',
    requireInteraction: false,
    actions: [
      {
        action: 'study',
        title: 'Estudiar ara'
      },
      {
        action: 'later',
        title: 'Més tard'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('CatalanPro', options)
  );
});

/**
 * Handle notification clicks
 */
self.addEventListener('notificationclick', (event) => {
  console.log('[SW] Notification clicked:', event.action);
  
  event.notification.close();
  
  if (event.action === 'study') {
    event.waitUntil(
      clients.openWindow('/?action=review')
    );
  } else if (event.action === 'later') {
    // Schedule another reminder
    console.log('[SW] Study reminder postponed');
  } else {
    // Default action - open app
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

/**
 * Handle messages from main thread
 */
self.addEventListener('message', (event) => {
  console.log('[SW] Message received:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'CACHE_DATA_FILE') {
    const { url } = event.data;
    caches.open(DATA_CACHE).then(cache => {
      return fetch(url).then(response => {
        if (response.ok) {
          return cache.put(url, response);
        }
      });
    });
  }
});

/**
 * Error handling
 */
self.addEventListener('error', (event) => {
  console.error('[SW] Service worker error:', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
  console.error('[SW] Unhandled promise rejection:', event.reason);
});

console.log('[SW] Service worker script loaded');
