# CatalanPro - Makefile for easy development commands

.PHONY: help start dev build stop logs clean install test

# Default target
.DEFAULT_GOAL := help

# Colors for output
BLUE := \033[36m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
RESET := \033[0m

help: ## Show this help message
	@echo "$(BLUE)🏛️ CatalanPro - Development Commands$(RESET)"
	@echo "======================================"
	@echo ""
	@echo "$(GREEN)Docker Commands:$(RESET)"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(BLUE)%-12s$(RESET) %s\n", $$1, $$2}'
	@echo ""
	@echo "$(GREEN)Examples:$(RESET)"
	@echo "  make start    # Start production app on http://localhost:8080"
	@echo "  make dev      # Start development server on http://localhost:8081"
	@echo "  make build    # Build dataset and start app"
	@echo ""

start: ## Start CatalanPro in production mode (Docker)
	@echo "$(GREEN)🚀 Starting CatalanPro in production mode...$(RESET)"
	@echo "$(BLUE)📱 App will be available at: http://localhost:8080$(RESET)"
	@docker-compose up --build -d catalanpro
	@echo "$(GREEN)✅ CatalanPro is running!$(RESET)"
	@echo "$(YELLOW)🔍 Check logs with: make logs$(RESET)"
	@echo "$(YELLOW)🛑 Stop with: make stop$(RESET)"

dev: ## Start development server with hot reload (Docker)
	@echo "$(GREEN)🔧 Starting CatalanPro in development mode...$(RESET)"
	@echo "$(BLUE)📱 Dev server will be available at: http://localhost:8081$(RESET)"
	@docker-compose --profile dev up --build catalanpro-dev

build: ## Build custom dataset and start production app
	@echo "$(GREEN)🛠️ Building CatalanPro dataset...$(RESET)"
	@docker-compose --profile build run --rm catalanpro-builder
	@echo "$(GREEN)✅ Dataset built! Starting production app...$(RESET)"
	@make start

stop: ## Stop all CatalanPro containers
	@echo "$(YELLOW)🛑 Stopping CatalanPro containers...$(RESET)"
	@docker-compose down
	@echo "$(GREEN)✅ Containers stopped$(RESET)"

logs: ## Show container logs
	@echo "$(BLUE)📋 CatalanPro container logs:$(RESET)"
	@echo "=============================="
	@docker-compose logs -f catalanpro

clean: ## Remove containers and images
	@echo "$(YELLOW)🧹 Cleaning up CatalanPro Docker resources...$(RESET)"
	@docker-compose down --rmi all --volumes --remove-orphans
	@docker image prune -f
	@echo "$(GREEN)✅ Cleanup complete$(RESET)"

install: ## Install build dependencies (local)
	@echo "$(GREEN)📦 Installing build dependencies...$(RESET)"
	@cd full-profile && npm install
	@echo "$(GREEN)✅ Dependencies installed$(RESET)"

test: ## Run tests and validation
	@echo "$(GREEN)🧪 Running tests and validation...$(RESET)"
	@cd full-profile && npm test
	@echo "$(GREEN)✅ Tests completed$(RESET)"

# Local development commands (without Docker)
start-local: ## Start app locally with Python server
	@echo "$(GREEN)🚀 Starting CatalanPro locally...$(RESET)"
	@echo "$(BLUE)📱 App will be available at: http://localhost:8080$(RESET)"
	@cd app && python3 -m http.server 8080

build-local: ## Build dataset locally
	@echo "$(GREEN)🛠️ Building dataset locally...$(RESET)"
	@cd full-profile && node build_decks.js --profile=lite --output=../app/data --verbose
	@echo "$(GREEN)✅ Dataset built!$(RESET)"

# Quick demo
demo: ## Run quick demo (local Python server)
	@echo "$(GREEN)🎬 Starting CatalanPro demo...$(RESET)"
	@./demo.sh

# Health check
health: ## Check if the app is running
	@echo "$(BLUE)🔍 Checking CatalanPro health...$(RESET)"
	@curl -f http://localhost:8080/health 2>/dev/null && echo "$(GREEN)✅ App is healthy$(RESET)" || echo "$(RED)❌ App is not responding$(RESET)"

# Open browser
open: ## Open CatalanPro in browser
	@echo "$(BLUE)🌐 Opening CatalanPro in browser...$(RESET)"
	@open http://localhost:8080 2>/dev/null || xdg-open http://localhost:8080 2>/dev/null || echo "$(YELLOW)Please open http://localhost:8080 manually$(RESET)"
