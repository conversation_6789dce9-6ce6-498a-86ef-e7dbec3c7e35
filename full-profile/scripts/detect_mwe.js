// Multi-word expression detection
// Detects collocations and common phrases using PMI and frequency analysis

const fs = require('fs');
const path = require('path');

class MWEDetector {
    constructor() {
        this.ngramCounts = new Map();
        this.wordCounts = new Map();
        this.totalWords = 0;
        this.mweThreshold = 2.0; // PMI threshold
    }

    async run(workingDir, profile) {
        console.log(`🔍 Detecting multi-word expressions (profile: ${profile})`);
        
        // Collect n-gram statistics
        await this.collectNgramStats(workingDir);
        
        // Calculate PMI scores
        const mwes = this.detectMWEs();
        
        // Filter and rank MWEs
        const filteredMWEs = this.filterMWEs(mwes, profile);
        
        // Save results
        await this.saveMWEs(workingDir, filteredMWEs);
        
        return {
            profile,
            totalNgrams: this.ngramCounts.size,
            detectedMWEs: filteredMWEs.length,
            threshold: this.mweThreshold
        };
    }

    async collectNgramStats(workingDir) {
        const files = ['analyzed_sentences.jsonl', 'analyzed_terms.jsonl'];
        
        for (const filename of files) {
            const filepath = path.join(workingDir, filename);
            if (!fs.existsSync(filepath)) continue;
            
            console.log(`Processing ${filename} for n-grams...`);
            
            const lines = fs.readFileSync(filepath, 'utf8').split('\n');
            
            for (const line of lines) {
                if (!line.trim()) continue;
                
                try {
                    const item = JSON.parse(line);
                    this.processText(item.catalan);
                } catch (error) {
                    // Skip invalid lines
                }
            }
        }
        
        console.log(`Collected stats for ${this.wordCounts.size} words and ${this.ngramCounts.size} n-grams`);
    }

    processText(text) {
        const words = this.tokenize(text);
        
        // Count individual words
        for (const word of words) {
            this.wordCounts.set(word, (this.wordCounts.get(word) || 0) + 1);
            this.totalWords++;
        }
        
        // Count bigrams
        for (let i = 0; i < words.length - 1; i++) {
            const bigram = `${words[i]} ${words[i + 1]}`;
            this.ngramCounts.set(bigram, (this.ngramCounts.get(bigram) || 0) + 1);
        }
        
        // Count trigrams
        for (let i = 0; i < words.length - 2; i++) {
            const trigram = `${words[i]} ${words[i + 1]} ${words[i + 2]}`;
            this.ngramCounts.set(trigram, (this.ngramCounts.get(trigram) || 0) + 1);
        }
    }

    tokenize(text) {
        return text
            .toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 1);
    }

    detectMWEs() {
        const mwes = [];
        
        for (const [ngram, count] of this.ngramCounts) {
            if (count < 3) continue; // Minimum frequency threshold
            
            const words = ngram.split(' ');
            if (words.length < 2 || words.length > 3) continue;
            
            const pmi = this.calculatePMI(words, count);
            
            if (pmi >= this.mweThreshold) {
                mwes.push({
                    phrase: ngram,
                    words: words,
                    frequency: count,
                    pmi: pmi,
                    type: this.classifyMWE(words)
                });
            }
        }
        
        return mwes.sort((a, b) => b.pmi - a.pmi);
    }

    calculatePMI(words, ngramCount) {
        // Pointwise Mutual Information calculation
        const ngramProb = ngramCount / this.totalWords;
        
        let independentProb = 1;
        for (const word of words) {
            const wordCount = this.wordCounts.get(word) || 1;
            const wordProb = wordCount / this.totalWords;
            independentProb *= wordProb;
        }
        
        return Math.log2(ngramProb / independentProb);
    }

    classifyMWE(words) {
        // Simple MWE classification
        const patterns = {
            'verb_noun': /^(fer|tenir|donar|posar)\s+\w+$/,
            'adj_noun': /^\w+\s+(casa|dia|temps|any)$/,
            'prep_phrase': /^(a|de|en|per|amb)\s+\w+$/,
            'compound': /^\w+\s+\w+$/
        };
        
        const phrase = words.join(' ');
        
        for (const [type, pattern] of Object.entries(patterns)) {
            if (pattern.test(phrase)) {
                return type;
            }
        }
        
        return 'other';
    }

    filterMWEs(mwes, profile) {
        let maxMWEs;
        let minFrequency;
        
        switch (profile) {
            case 'lite':
                maxMWEs = 50;
                minFrequency = 5;
                break;
            case 'standard':
                maxMWEs = 500;
                minFrequency = 3;
                break;
            case 'full':
                maxMWEs = 2000;
                minFrequency = 2;
                break;
            default:
                maxMWEs = 100;
                minFrequency = 3;
        }
        
        return mwes
            .filter(mwe => mwe.frequency >= minFrequency)
            .filter(mwe => this.isValidMWE(mwe))
            .slice(0, maxMWEs);
    }

    isValidMWE(mwe) {
        // Filter out invalid MWEs
        const phrase = mwe.phrase;
        
        // Skip if contains numbers or special characters
        if (/\d/.test(phrase)) return false;
        
        // Skip very short or very long phrases
        if (phrase.length < 5 || phrase.length > 30) return false;
        
        // Skip if all words are very common (likely not a true MWE)
        const commonWords = new Set(['el', 'de', 'que', 'i', 'a', 'en', 'un', 'és', 'se', 'no']);
        if (mwe.words.every(word => commonWords.has(word))) return false;
        
        return true;
    }

    async saveMWEs(workingDir, mwes) {
        const outputFile = path.join(workingDir, 'detected_mwes.jsonl');
        
        const content = mwes.map(mwe => JSON.stringify(mwe)).join('\n');
        fs.writeFileSync(outputFile, content, 'utf8');
        
        console.log(`Saved ${mwes.length} multi-word expressions`);
        
        // Also save a summary
        const summaryFile = path.join(workingDir, 'mwe_summary.txt');
        const summary = mwes.map(mwe => 
            `${mwe.phrase}\t${mwe.frequency}\t${mwe.pmi.toFixed(2)}\t${mwe.type}`
        ).join('\n');
        
        fs.writeFileSync(summaryFile, `phrase\tfrequency\tpmi\ttype\n${summary}`, 'utf8');
    }
}

module.exports = {
    run: async (workingDir, profile) => {
        const detector = new MWEDetector();
        return detector.run(workingDir, profile);
    }
};
