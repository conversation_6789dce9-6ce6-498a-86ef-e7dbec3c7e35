// Final export script
// Exports processed data to JSONL files for app consumption

const fs = require('fs');
const path = require('path');

class DeckExporter {
    constructor() {
        this.exportStats = {
            decksExported: 0,
            cardsExported: 0,
            mediaExported: 0,
            filesCreated: []
        };
    }

    async run(workingDir, outputDir, profile) {
        console.log(`📦 Exporting decks to ${outputDir} (profile: ${profile})`);
        
        // Ensure output directory exists
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        
        // Load processed data
        const cards = await this.loadCards(workingDir);
        const decks = await this.loadDecks(workingDir);
        const audioManifest = await this.loadAudioManifest(workingDir);
        
        // Export decks
        await this.exportDecks(decks, outputDir);
        
        // Export cards by CEFR level and type
        await this.exportCardsByLevel(cards, outputDir);
        
        // Export media manifest
        await this.exportMediaManifest(audioManifest, outputDir);
        
        // Create media directory structure
        await this.createMediaStructure(outputDir);
        
        console.log('Export Summary:');
        console.log(`  Decks: ${this.exportStats.decksExported}`);
        console.log(`  Cards: ${this.exportStats.cardsExported}`);
        console.log(`  Media entries: ${this.exportStats.mediaExported}`);
        console.log(`  Files created: ${this.exportStats.filesCreated.length}`);
        
        return {
            profile,
            stats: this.exportStats,
            outputDir
        };
    }

    async loadCards(workingDir) {
        const filepath = path.join(workingDir, 'cards_with_audio.jsonl');
        return this.loadJSONL(filepath);
    }

    async loadDecks(workingDir) {
        const filepath = path.join(workingDir, 'generated_decks.jsonl');
        return this.loadJSONL(filepath);
    }

    async loadAudioManifest(workingDir) {
        const filepath = path.join(workingDir, 'audio_manifest.jsonl');
        return this.loadJSONL(filepath);
    }

    async loadJSONL(filepath) {
        if (!fs.existsSync(filepath)) {
            console.warn(`File not found: ${filepath}`);
            return [];
        }
        
        const lines = fs.readFileSync(filepath, 'utf8').split('\n');
        const items = [];
        
        for (const line of lines) {
            if (!line.trim()) continue;
            
            try {
                items.push(JSON.parse(line));
            } catch (error) {
                console.warn(`Invalid JSON line in ${filepath}: ${line.substring(0, 50)}...`);
            }
        }
        
        return items;
    }

    async exportDecks(decks, outputDir) {
        const outputFile = path.join(outputDir, 'decks_topics.jsonl');
        
        // Update deck counts based on actual cards
        const updatedDecks = decks.map(deck => ({
            ...deck,
            id: deck.id,
            title: deck.title,
            description: deck.description,
            tags: deck.tags || [],
            counts: deck.counts || { total: 0, new: 0, due: 0 }
        }));
        
        const content = updatedDecks.map(deck => JSON.stringify(deck)).join('\n');
        fs.writeFileSync(outputFile, content, 'utf8');
        
        this.exportStats.decksExported = updatedDecks.length;
        this.exportStats.filesCreated.push('decks_topics.jsonl');
        
        console.log(`Exported ${updatedDecks.length} decks`);
    }

    async exportCardsByLevel(cards, outputDir) {
        // Group cards by CEFR level and type
        const cardGroups = {
            terms: { A1: [], A2: [], B1: [], B2: [], C1: [], C2: [] },
            sentences: { A1: [], A2: [], B1: [], B2: [], C1: [], C2: [] },
            conjugations: [],
            other: []
        };
        
        for (const card of cards) {
            const cefr = card.cefr || 'A1';
            
            if (card.type === 'term') {
                if (cardGroups.terms[cefr]) {
                    cardGroups.terms[cefr].push(card);
                }
            } else if (card.type === 'sentence' || card.type === 'cloze') {
                if (cardGroups.sentences[cefr]) {
                    cardGroups.sentences[cefr].push(card);
                }
            } else if (card.type === 'conjugation') {
                cardGroups.conjugations.push(card);
            } else {
                cardGroups.other.push(card);
            }
        }
        
        // Export term cards by level
        for (const level of ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']) {
            const termCards = cardGroups.terms[level];
            if (termCards.length > 0) {
                const filename = `cards_terms_${level}.jsonl`;
                const filepath = path.join(outputDir, filename);
                const content = termCards.map(card => JSON.stringify(card)).join('\n');
                fs.writeFileSync(filepath, content, 'utf8');
                
                this.exportStats.cardsExported += termCards.length;
                this.exportStats.filesCreated.push(filename);
                
                console.log(`Exported ${termCards.length} ${level} term cards`);
            }
        }
        
        // Export sentence cards by level
        for (const level of ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']) {
            const sentenceCards = cardGroups.sentences[level];
            if (sentenceCards.length > 0) {
                const filename = `cards_sentences_${level}.jsonl`;
                const filepath = path.join(outputDir, filename);
                const content = sentenceCards.map(card => JSON.stringify(card)).join('\n');
                fs.writeFileSync(filepath, content, 'utf8');
                
                this.exportStats.cardsExported += sentenceCards.length;
                this.exportStats.filesCreated.push(filename);
                
                console.log(`Exported ${sentenceCards.length} ${level} sentence cards`);
            }
        }
        
        // Export conjugation cards
        if (cardGroups.conjugations.length > 0) {
            const filename = 'cards_conjugations.jsonl';
            const filepath = path.join(outputDir, filename);
            const content = cardGroups.conjugations.map(card => JSON.stringify(card)).join('\n');
            fs.writeFileSync(filepath, content, 'utf8');
            
            this.exportStats.cardsExported += cardGroups.conjugations.length;
            this.exportStats.filesCreated.push(filename);
            
            console.log(`Exported ${cardGroups.conjugations.length} conjugation cards`);
        }
        
        // Export other cards if any
        if (cardGroups.other.length > 0) {
            const filename = 'cards_other.jsonl';
            const filepath = path.join(outputDir, filename);
            const content = cardGroups.other.map(card => JSON.stringify(card)).join('\n');
            fs.writeFileSync(filepath, content, 'utf8');
            
            this.exportStats.cardsExported += cardGroups.other.length;
            this.exportStats.filesCreated.push(filename);
            
            console.log(`Exported ${cardGroups.other.length} other cards`);
        }
    }

    async exportMediaManifest(audioManifest, outputDir) {
        const outputFile = path.join(outputDir, 'media_manifest.jsonl');
        
        // Clean and validate audio entries
        const validEntries = audioManifest.filter(entry => 
            entry.id && entry.filename && entry.kind === 'audio'
        );
        
        const content = validEntries.map(entry => JSON.stringify(entry)).join('\n');
        fs.writeFileSync(outputFile, content, 'utf8');
        
        this.exportStats.mediaExported = validEntries.length;
        this.exportStats.filesCreated.push('media_manifest.jsonl');
        
        console.log(`Exported ${validEntries.length} media entries`);
    }

    async createMediaStructure(outputDir) {
        const mediaDir = path.join(outputDir, '..', 'media');
        const audioDir = path.join(mediaDir, 'audio', 'ca');
        
        // Create directory structure
        if (!fs.existsSync(audioDir)) {
            fs.mkdirSync(audioDir, { recursive: true });
            console.log(`Created media directory: ${audioDir}`);
        }
        
        // Create placeholder audio files or README
        const readmePath = path.join(audioDir, 'README.md');
        const readmeContent = `# Audio Files

This directory should contain Catalan audio files referenced in the media manifest.

## File naming convention:
- Normal speed: \`word_or_phrase.mp3\`
- Slow speed: \`word_or_phrase_slow.mp3\`

## Generating audio files:
1. Use the SSML files in the build output to generate TTS audio
2. Use tools like espeak, festival, or cloud TTS services
3. Ensure files match the filenames in media_manifest.jsonl

## Example commands:
\`\`\`bash
# Using espeak for basic TTS
espeak -v ca -s 150 -w output.wav "text to speak"

# Convert to MP3
ffmpeg -i output.wav output.mp3
\`\`\`
`;
        
        fs.writeFileSync(readmePath, readmeContent, 'utf8');
        
        // Create a few sample placeholder files
        const sampleFiles = ['hola.mp3', 'gracies.mp3', 'casa.mp3'];
        for (const filename of sampleFiles) {
            const filepath = path.join(audioDir, filename);
            if (!fs.existsSync(filepath)) {
                // Create empty placeholder file
                fs.writeFileSync(filepath, '', 'utf8');
            }
        }
    }
}

module.exports = {
    run: async (workingDir, outputDir, profile) => {
        const exporter = new DeckExporter();
        return exporter.run(workingDir, outputDir, profile);
    }
};
