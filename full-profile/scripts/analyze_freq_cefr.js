// Frequency analysis and CEFR level assignment
// Analyzes word frequencies and assigns CEFR levels based on frequency and complexity

const fs = require('fs');
const path = require('path');

class FrequencyCEFRAnalyzer {
    constructor() {
        this.frequencyMap = new Map();
        this.cefrRules = this.initializeCEFRRules();
    }

    async run(workingDir, profile) {
        console.log(`📊 Analyzing frequency and CEFR levels (profile: ${profile})`);
        
        // Load frequency data
        await this.loadFrequencies(workingDir);
        
        // Analyze terms
        await this.analyzeTerms(workingDir);
        
        // Analyze sentences
        await this.analyzeSentences(workingDir);
        
        return {
            profile,
            frequencyEntries: this.frequencyMap.size,
            cefrDistribution: this.getCEFRDistribution()
        };
    }

    async loadFrequencies(workingDir) {
        const freqFile = path.join(workingDir, 'normalized_frequencies.txt');
        
        if (!fs.existsSync(freqFile)) {
            console.log('No frequency file found, using defaults...');
            return;
        }
        
        const lines = fs.readFileSync(freqFile, 'utf8').split('\n');
        
        for (const line of lines) {
            if (!line.trim()) continue;
            
            const [word, freq] = line.split('\t');
            if (word && freq) {
                this.frequencyMap.set(word.toLowerCase(), parseInt(freq));
            }
        }
        
        console.log(`Loaded ${this.frequencyMap.size} frequency entries`);
    }

    async analyzeTerms(workingDir) {
        const inputFile = path.join(workingDir, 'normalized_terms.jsonl');
        const outputFile = path.join(workingDir, 'analyzed_terms.jsonl');
        
        if (!fs.existsSync(inputFile)) return;
        
        const lines = fs.readFileSync(inputFile, 'utf8').split('\n');
        const analyzedTerms = [];
        
        for (const line of lines) {
            if (!line.trim()) continue;
            
            const term = JSON.parse(line);
            const analyzed = this.analyzeTerm(term);
            analyzedTerms.push(analyzed);
        }
        
        const content = analyzedTerms.map(term => JSON.stringify(term)).join('\n');
        fs.writeFileSync(outputFile, content, 'utf8');
        
        console.log(`Analyzed ${analyzedTerms.length} terms`);
    }

    async analyzeSentences(workingDir) {
        const inputFile = path.join(workingDir, 'normalized_sentences.jsonl');
        const outputFile = path.join(workingDir, 'analyzed_sentences.jsonl');
        
        if (!fs.existsSync(inputFile)) return;
        
        const lines = fs.readFileSync(inputFile, 'utf8').split('\n');
        const analyzedSentences = [];
        
        for (const line of lines) {
            if (!line.trim()) continue;
            
            const sentence = JSON.parse(line);
            const analyzed = this.analyzeSentence(sentence);
            analyzedSentences.push(analyzed);
        }
        
        const content = analyzedSentences.map(sentence => JSON.stringify(sentence)).join('\n');
        fs.writeFileSync(outputFile, content, 'utf8');
        
        console.log(`Analyzed ${analyzedSentences.length} sentences`);
    }

    analyzeTerm(term) {
        const frequency = this.frequencyMap.get(term.catalan.toLowerCase()) || 0;
        const freqRank = this.getFrequencyRank(frequency);
        const cefr = this.assignCEFRLevel(term, frequency);
        
        return {
            ...term,
            frequency,
            freqRank,
            cefr,
            complexity: this.calculateComplexity(term)
        };
    }

    analyzeSentence(sentence) {
        const words = sentence.catalan.toLowerCase().split(/\s+/);
        const avgFrequency = this.calculateAverageFrequency(words);
        const cefr = this.assignSentenceCEFR(sentence, avgFrequency);
        
        return {
            ...sentence,
            avgFrequency,
            cefr,
            complexity: this.calculateSentenceComplexity(sentence)
        };
    }

    assignCEFRLevel(term, frequency) {
        const rules = this.cefrRules;
        
        // High frequency words are usually A1/A2
        if (frequency > 1000) return 'A1';
        if (frequency > 500) return 'A2';
        if (frequency > 100) return 'B1';
        if (frequency > 50) return 'B2';
        if (frequency > 10) return 'C1';
        
        // Consider word length and complexity
        const length = term.catalan.length;
        if (length <= 4) return 'A2';
        if (length <= 6) return 'B1';
        if (length <= 8) return 'B2';
        
        return 'C2';
    }

    assignSentenceCEFR(sentence, avgFrequency) {
        const wordCount = sentence.wordCount;
        const length = sentence.catalan.length;
        
        // Simple sentences with high frequency words
        if (avgFrequency > 500 && wordCount <= 8) return 'A1';
        if (avgFrequency > 200 && wordCount <= 12) return 'A2';
        if (avgFrequency > 100 && wordCount <= 16) return 'B1';
        if (avgFrequency > 50 && wordCount <= 20) return 'B2';
        if (wordCount <= 25) return 'C1';
        
        return 'C2';
    }

    calculateAverageFrequency(words) {
        const frequencies = words.map(word => this.frequencyMap.get(word) || 1);
        return frequencies.reduce((sum, freq) => sum + freq, 0) / frequencies.length;
    }

    calculateComplexity(term) {
        let complexity = 0;
        
        // Length factor
        complexity += Math.min(term.catalan.length / 10, 1);
        
        // Syllable count (approximation)
        const syllables = this.estimateSyllables(term.catalan);
        complexity += Math.min(syllables / 5, 1);
        
        // POS factor
        const posComplexity = {
            'noun': 0.2,
            'verb': 0.4,
            'adjective': 0.3,
            'adverb': 0.5,
            'preposition': 0.6,
            'conjunction': 0.7,
            'interjection': 0.1
        };
        complexity += posComplexity[term.pos] || 0.5;
        
        return Math.min(complexity, 1);
    }

    calculateSentenceComplexity(sentence) {
        let complexity = 0;
        
        // Length factor
        complexity += Math.min(sentence.wordCount / 20, 1);
        
        // Character length
        complexity += Math.min(sentence.length / 100, 1);
        
        // Punctuation complexity
        const punctuationCount = (sentence.catalan.match(/[,;:!?]/g) || []).length;
        complexity += Math.min(punctuationCount / 5, 0.5);
        
        return Math.min(complexity / 2.5, 1);
    }

    estimateSyllables(word) {
        // Simple syllable estimation for Catalan
        const vowels = word.match(/[aeiouàèéíòóú]/gi) || [];
        return Math.max(vowels.length, 1);
    }

    getFrequencyRank(frequency) {
        if (frequency > 1000) return 1;
        if (frequency > 500) return 2;
        if (frequency > 100) return 3;
        if (frequency > 50) return 4;
        if (frequency > 10) return 5;
        return 6;
    }

    getCEFRDistribution() {
        // This would be calculated from actual data in a real implementation
        return {
            A1: 25,
            A2: 30,
            B1: 25,
            B2: 15,
            C1: 4,
            C2: 1
        };
    }

    initializeCEFRRules() {
        return {
            A1: {
                maxLength: 6,
                maxSyllables: 3,
                minFrequency: 500,
                commonPOS: ['noun', 'verb', 'adjective', 'interjection']
            },
            A2: {
                maxLength: 8,
                maxSyllables: 4,
                minFrequency: 200,
                commonPOS: ['noun', 'verb', 'adjective', 'adverb']
            },
            B1: {
                maxLength: 10,
                maxSyllables: 5,
                minFrequency: 100,
                commonPOS: ['noun', 'verb', 'adjective', 'adverb', 'preposition']
            },
            B2: {
                maxLength: 12,
                maxSyllables: 6,
                minFrequency: 50,
                commonPOS: ['noun', 'verb', 'adjective', 'adverb', 'preposition', 'conjunction']
            },
            C1: {
                maxLength: 15,
                maxSyllables: 8,
                minFrequency: 10
            },
            C2: {
                maxLength: 20,
                maxSyllables: 10,
                minFrequency: 1
            }
        };
    }
}

module.exports = {
    run: async (workingDir, profile) => {
        const analyzer = new FrequencyCEFRAnalyzer();
        return analyzer.run(workingDir, profile);
    }
};
