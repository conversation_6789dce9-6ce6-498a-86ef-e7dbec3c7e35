// Data validation script
// Validates generated cards and performs quality assurance checks

const fs = require('fs');
const path = require('path');

class DataValidator {
    constructor() {
        this.validationResults = {
            cards: { total: 0, valid: 0, errors: [] },
            decks: { total: 0, valid: 0, errors: [] },
            audio: { total: 0, valid: 0, errors: [] },
            overall: { valid: true, warnings: [], errors: [] }
        };
    }

    async run(workingDir, profile) {
        console.log(`✅ Validating generated data (profile: ${profile})`);
        
        // Validate cards
        await this.validateCards(workingDir);
        
        // Validate decks
        await this.validateDecks(workingDir);
        
        // Validate audio manifest
        await this.validateAudioManifest(workingDir);
        
        // Cross-reference validation
        await this.validateCrossReferences(workingDir);
        
        // Generate validation report
        await this.generateValidationReport(workingDir);
        
        return {
            profile,
            valid: this.validationResults.overall.valid,
            results: this.validationResults
        };
    }

    async validateCards(workingDir) {
        const inputFile = path.join(workingDir, 'cards_with_audio.jsonl');
        
        if (!fs.existsSync(inputFile)) {
            this.validationResults.overall.errors.push('Cards file not found');
            return;
        }
        
        console.log('Validating cards...');
        
        const lines = fs.readFileSync(inputFile, 'utf8').split('\n');
        
        for (const line of lines) {
            if (!line.trim()) continue;
            
            this.validationResults.cards.total++;
            
            try {
                const card = JSON.parse(line);
                const errors = this.validateCard(card);
                
                if (errors.length === 0) {
                    this.validationResults.cards.valid++;
                } else {
                    this.validationResults.cards.errors.push({
                        cardId: card.id,
                        errors: errors
                    });
                }
            } catch (error) {
                this.validationResults.cards.errors.push({
                    cardId: 'unknown',
                    errors: [`JSON parse error: ${error.message}`]
                });
            }
        }
        
        console.log(`Validated ${this.validationResults.cards.total} cards, ${this.validationResults.cards.valid} valid`);
    }

    validateCard(card) {
        const errors = [];
        
        // Required fields
        if (!card.id) errors.push('Missing id');
        if (!card.type) errors.push('Missing type');
        if (!card.catalan) errors.push('Missing catalan text');
        if (!card.deckId) errors.push('Missing deckId');
        if (!card.cefr) errors.push('Missing CEFR level');
        
        // Type validation
        const validTypes = ['term', 'sentence', 'conjugation', 'listening', 'cloze'];
        if (card.type && !validTypes.includes(card.type)) {
            errors.push(`Invalid type: ${card.type}`);
        }
        
        // CEFR validation
        const validCEFR = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'];
        if (card.cefr && !validCEFR.includes(card.cefr)) {
            errors.push(`Invalid CEFR level: ${card.cefr}`);
        }
        
        // Text validation
        if (card.catalan) {
            if (card.catalan.length < 1) errors.push('Catalan text too short');
            if (card.catalan.length > 500) errors.push('Catalan text too long');
            if (!/^[a-zA-ZàèéíòóúüçñÀÈÉÍÒÓÚÜÇÑ\s\-'.,!?()]+$/.test(card.catalan)) {
                errors.push('Catalan text contains invalid characters');
            }
        }
        
        // Translations validation
        if (card.translations) {
            const validLangs = ['es', 'en', 'ru'];
            for (const lang of validLangs) {
                if (card.translations[lang] && card.translations[lang].length > 500) {
                    errors.push(`Translation ${lang} too long`);
                }
            }
        }
        
        // Numeric field validation
        if (card.ease && (card.ease < 1.3 || card.ease > 5.0)) {
            errors.push('Invalid ease value');
        }
        if (card.interval && card.interval < 0) {
            errors.push('Invalid interval value');
        }
        if (card.stability && card.stability < 0) {
            errors.push('Invalid stability value');
        }
        if (card.difficulty && (card.difficulty < 1 || card.difficulty > 10)) {
            errors.push('Invalid difficulty value');
        }
        
        // Array field validation
        if (card.audioIds && !Array.isArray(card.audioIds)) {
            errors.push('audioIds must be an array');
        }
        if (card.examples && !Array.isArray(card.examples)) {
            errors.push('examples must be an array');
        }
        if (card.tags && !Array.isArray(card.tags)) {
            errors.push('tags must be an array');
        }
        
        return errors;
    }

    async validateDecks(workingDir) {
        const inputFile = path.join(workingDir, 'generated_decks.jsonl');
        
        if (!fs.existsSync(inputFile)) {
            this.validationResults.overall.errors.push('Decks file not found');
            return;
        }
        
        console.log('Validating decks...');
        
        const lines = fs.readFileSync(inputFile, 'utf8').split('\n');
        
        for (const line of lines) {
            if (!line.trim()) continue;
            
            this.validationResults.decks.total++;
            
            try {
                const deck = JSON.parse(line);
                const errors = this.validateDeck(deck);
                
                if (errors.length === 0) {
                    this.validationResults.decks.valid++;
                } else {
                    this.validationResults.decks.errors.push({
                        deckId: deck.id,
                        errors: errors
                    });
                }
            } catch (error) {
                this.validationResults.decks.errors.push({
                    deckId: 'unknown',
                    errors: [`JSON parse error: ${error.message}`]
                });
            }
        }
        
        console.log(`Validated ${this.validationResults.decks.total} decks, ${this.validationResults.decks.valid} valid`);
    }

    validateDeck(deck) {
        const errors = [];
        
        // Required fields
        if (!deck.id) errors.push('Missing id');
        if (!deck.title) errors.push('Missing title');
        if (!deck.description) errors.push('Missing description');
        
        // Field validation
        if (deck.title && deck.title.length > 100) {
            errors.push('Title too long');
        }
        if (deck.description && deck.description.length > 500) {
            errors.push('Description too long');
        }
        
        // Counts validation
        if (deck.counts) {
            if (typeof deck.counts.total !== 'number' || deck.counts.total < 0) {
                errors.push('Invalid total count');
            }
            if (typeof deck.counts.new !== 'number' || deck.counts.new < 0) {
                errors.push('Invalid new count');
            }
            if (typeof deck.counts.due !== 'number' || deck.counts.due < 0) {
                errors.push('Invalid due count');
            }
        }
        
        return errors;
    }

    async validateAudioManifest(workingDir) {
        const inputFile = path.join(workingDir, 'audio_manifest.jsonl');
        
        if (!fs.existsSync(inputFile)) {
            this.validationResults.overall.warnings.push('Audio manifest file not found');
            return;
        }
        
        console.log('Validating audio manifest...');
        
        const lines = fs.readFileSync(inputFile, 'utf8').split('\n');
        
        for (const line of lines) {
            if (!line.trim()) continue;
            
            this.validationResults.audio.total++;
            
            try {
                const audio = JSON.parse(line);
                const errors = this.validateAudioEntry(audio);
                
                if (errors.length === 0) {
                    this.validationResults.audio.valid++;
                } else {
                    this.validationResults.audio.errors.push({
                        audioId: audio.id,
                        errors: errors
                    });
                }
            } catch (error) {
                this.validationResults.audio.errors.push({
                    audioId: 'unknown',
                    errors: [`JSON parse error: ${error.message}`]
                });
            }
        }
        
        console.log(`Validated ${this.validationResults.audio.total} audio entries, ${this.validationResults.audio.valid} valid`);
    }

    validateAudioEntry(audio) {
        const errors = [];
        
        // Required fields
        if (!audio.id) errors.push('Missing id');
        if (!audio.filename) errors.push('Missing filename');
        if (!audio.kind) errors.push('Missing kind');
        if (!audio.variant) errors.push('Missing variant');
        
        // Field validation
        if (audio.kind && audio.kind !== 'audio') {
            errors.push(`Invalid kind: ${audio.kind}`);
        }
        
        const validVariants = ['normal', 'slow'];
        if (audio.variant && !validVariants.includes(audio.variant)) {
            errors.push(`Invalid variant: ${audio.variant}`);
        }
        
        if (audio.duration && (typeof audio.duration !== 'number' || audio.duration <= 0)) {
            errors.push('Invalid duration');
        }
        
        if (audio.filename && !audio.filename.match(/\.(mp3|wav|ogg)$/)) {
            errors.push('Invalid audio file extension');
        }
        
        return errors;
    }

    async validateCrossReferences(workingDir) {
        console.log('Validating cross-references...');
        
        // Load all data
        const cards = await this.loadJSONL(path.join(workingDir, 'cards_with_audio.jsonl'));
        const decks = await this.loadJSONL(path.join(workingDir, 'generated_decks.jsonl'));
        const audio = await this.loadJSONL(path.join(workingDir, 'audio_manifest.jsonl'));
        
        // Create lookup maps
        const deckIds = new Set(decks.map(d => d.id));
        const audioIds = new Set(audio.map(a => a.id));
        
        // Validate deck references
        for (const card of cards) {
            if (card.deckId && !deckIds.has(card.deckId)) {
                this.validationResults.overall.errors.push(
                    `Card ${card.id} references non-existent deck: ${card.deckId}`
                );
            }
        }
        
        // Validate audio references
        for (const card of cards) {
            if (card.audioIds) {
                for (const audioId of card.audioIds) {
                    if (!audioIds.has(audioId)) {
                        this.validationResults.overall.warnings.push(
                            `Card ${card.id} references non-existent audio: ${audioId}`
                        );
                    }
                }
            }
        }
        
        // Check for orphaned audio
        const referencedAudioIds = new Set();
        for (const card of cards) {
            if (card.audioIds) {
                card.audioIds.forEach(id => referencedAudioIds.add(id));
            }
        }
        
        for (const audioEntry of audio) {
            if (!referencedAudioIds.has(audioEntry.id)) {
                this.validationResults.overall.warnings.push(
                    `Orphaned audio entry: ${audioEntry.id}`
                );
            }
        }
    }

    async loadJSONL(filepath) {
        if (!fs.existsSync(filepath)) return [];
        
        const lines = fs.readFileSync(filepath, 'utf8').split('\n');
        const items = [];
        
        for (const line of lines) {
            if (!line.trim()) continue;
            
            try {
                items.push(JSON.parse(line));
            } catch (error) {
                // Skip invalid lines
            }
        }
        
        return items;
    }

    async generateValidationReport(workingDir) {
        const reportFile = path.join(workingDir, 'validation_report.json');
        
        // Calculate overall validity
        const totalErrors = this.validationResults.cards.errors.length +
                          this.validationResults.decks.errors.length +
                          this.validationResults.audio.errors.length +
                          this.validationResults.overall.errors.length;
        
        this.validationResults.overall.valid = totalErrors === 0;
        
        // Add summary statistics
        this.validationResults.summary = {
            totalCards: this.validationResults.cards.total,
            validCards: this.validationResults.cards.valid,
            cardErrorRate: this.validationResults.cards.total > 0 ? 
                (this.validationResults.cards.errors.length / this.validationResults.cards.total * 100).toFixed(2) + '%' : '0%',
            totalDecks: this.validationResults.decks.total,
            validDecks: this.validationResults.decks.valid,
            totalAudio: this.validationResults.audio.total,
            validAudio: this.validationResults.audio.valid,
            totalErrors: totalErrors,
            totalWarnings: this.validationResults.overall.warnings.length
        };
        
        fs.writeFileSync(reportFile, JSON.stringify(this.validationResults, null, 2), 'utf8');
        
        console.log('Validation Summary:');
        console.log(`  Cards: ${this.validationResults.cards.valid}/${this.validationResults.cards.total} valid`);
        console.log(`  Decks: ${this.validationResults.decks.valid}/${this.validationResults.decks.total} valid`);
        console.log(`  Audio: ${this.validationResults.audio.valid}/${this.validationResults.audio.total} valid`);
        console.log(`  Errors: ${totalErrors}`);
        console.log(`  Warnings: ${this.validationResults.overall.warnings.length}`);
        console.log(`  Overall: ${this.validationResults.overall.valid ? 'VALID' : 'INVALID'}`);
    }

    static async validateOutput(outputDir) {
        // Static method for validating final output
        const validator = new DataValidator();
        
        const files = [
            'decks_topics.jsonl',
            'media_manifest.jsonl',
            'cards_terms_A1.jsonl',
            'cards_terms_A2.jsonl',
            'cards_sentences_A1.jsonl',
            'cards_sentences_A2.jsonl'
        ];
        
        const stats = {
            filesFound: 0,
            totalCards: 0,
            totalDecks: 0,
            totalMedia: 0
        };
        
        const issues = [];
        
        for (const file of files) {
            const filepath = path.join(outputDir, file);
            
            if (fs.existsSync(filepath)) {
                stats.filesFound++;
                
                const lines = fs.readFileSync(filepath, 'utf8').split('\n').filter(l => l.trim());
                
                if (file.startsWith('cards_')) {
                    stats.totalCards += lines.length;
                } else if (file.startsWith('decks_')) {
                    stats.totalDecks += lines.length;
                } else if (file.startsWith('media_')) {
                    stats.totalMedia += lines.length;
                }
            } else {
                issues.push(`Missing file: ${file}`);
            }
        }
        
        return {
            valid: issues.length === 0,
            issues,
            stats
        };
    }
}

module.exports = {
    run: async (workingDir, profile) => {
        const validator = new DataValidator();
        return validator.run(workingDir, profile);
    },
    validateOutput: DataValidator.validateOutput
};
