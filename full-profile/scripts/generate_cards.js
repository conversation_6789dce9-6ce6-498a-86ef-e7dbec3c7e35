// Card generation script
// Generates different types of learning cards from processed data

const fs = require('fs');
const path = require('path');

class CardGenerator {
    constructor() {
        this.generatedCards = [];
        this.deckMap = new Map();
        this.cardIdCounter = 1;
    }

    async run(workingDir, profile) {
        console.log(`🎴 Generating cards (profile: ${profile})`);
        
        // Initialize decks
        this.initializeDecks();
        
        // Generate term cards
        await this.generateTermCards(workingDir, profile);
        
        // Generate sentence cards
        await this.generateSentenceCards(workingDir, profile);
        
        // Generate conjugation cards
        await this.generateConjugationCards(workingDir, profile);
        
        // Generate MWE cards
        await this.generateMWECards(workingDir, profile);
        
        // Save results
        await this.saveCards(workingDir);
        await this.saveDecks(workingDir);
        
        return {
            profile,
            totalCards: this.generatedCards.length,
            cardsByType: this.getCardTypeDistribution(),
            decks: this.deckMap.size
        };
    }

    initializeDecks() {
        const decks = [
            { id: 'vocab-a1', title: 'Vocabulario A1', description: 'Palabras básicas nivel A1', tags: ['vocabulario', 'A1'], cefr: 'A1' },
            { id: 'vocab-a2', title: 'Vocabulario A2', description: 'Palabras básicas nivel A2', tags: ['vocabulario', 'A2'], cefr: 'A2' },
            { id: 'vocab-b1', title: 'Vocabulario B1', description: 'Palabras intermedias nivel B1', tags: ['vocabulario', 'B1'], cefr: 'B1' },
            { id: 'vocab-b2', title: 'Vocabulario B2', description: 'Palabras intermedias nivel B2', tags: ['vocabulario', 'B2'], cefr: 'B2' },
            { id: 'vocab-c1', title: 'Vocabulario C1', description: 'Palabras avanzadas nivel C1', tags: ['vocabulario', 'C1'], cefr: 'C1' },
            { id: 'vocab-c2', title: 'Vocabulario C2', description: 'Palabras avanzadas nivel C2', tags: ['vocabulario', 'C2'], cefr: 'C2' },
            { id: 'sentences-a1', title: 'Frases A1', description: 'Frases básicas nivel A1', tags: ['frases', 'A1'], cefr: 'A1' },
            { id: 'sentences-a2', title: 'Frases A2', description: 'Frases básicas nivel A2', tags: ['frases', 'A2'], cefr: 'A2' },
            { id: 'sentences-b1', title: 'Frases B1', description: 'Frases intermedias nivel B1', tags: ['frases', 'B1'], cefr: 'B1' },
            { id: 'sentences-b2', title: 'Frases B2', description: 'Frases intermedias nivel B2', tags: ['frases', 'B2'], cefr: 'B2' },
            { id: 'sentences-c1', title: 'Frases C1', description: 'Frases avanzadas nivel C1', tags: ['frases', 'C1'], cefr: 'C1' },
            { id: 'sentences-c2', title: 'Frases C2', description: 'Frases avanzadas nivel C2', tags: ['frases', 'C2'], cefr: 'C2' },
            { id: 'conjugations', title: 'Conjugaciones', description: 'Formas verbales', tags: ['verbos', 'conjugación'], cefr: 'B1' },
            { id: 'expressions', title: 'Expresiones', description: 'Expresiones y frases hechas', tags: ['expresiones', 'cultura'], cefr: 'B2' }
        ];
        
        for (const deck of decks) {
            this.deckMap.set(deck.id, { ...deck, counts: { total: 0, new: 0, due: 0 } });
        }
    }

    async generateTermCards(workingDir, profile) {
        const inputFile = path.join(workingDir, 'analyzed_terms.jsonl');
        if (!fs.existsSync(inputFile)) return;
        
        console.log('Generating term cards...');
        
        const lines = fs.readFileSync(inputFile, 'utf8').split('\n');
        let termCount = 0;
        
        for (const line of lines) {
            if (!line.trim()) continue;
            
            try {
                const term = JSON.parse(line);
                const card = this.createTermCard(term);
                
                if (card) {
                    this.generatedCards.push(card);
                    this.updateDeckCounts(card.deckId);
                    termCount++;
                }
            } catch (error) {
                // Skip invalid entries
            }
        }
        
        console.log(`Generated ${termCount} term cards`);
    }

    async generateSentenceCards(workingDir, profile) {
        const inputFile = path.join(workingDir, 'analyzed_sentences.jsonl');
        if (!fs.existsSync(inputFile)) return;
        
        console.log('Generating sentence cards...');
        
        const lines = fs.readFileSync(inputFile, 'utf8').split('\n');
        let sentenceCount = 0;
        
        for (const line of lines) {
            if (!line.trim()) continue;
            
            try {
                const sentence = JSON.parse(line);
                const card = this.createSentenceCard(sentence);
                
                if (card) {
                    this.generatedCards.push(card);
                    this.updateDeckCounts(card.deckId);
                    sentenceCount++;
                    
                    // Also create cloze variant
                    const clozeCard = this.createClozeCard(sentence);
                    if (clozeCard) {
                        this.generatedCards.push(clozeCard);
                        this.updateDeckCounts(clozeCard.deckId);
                        sentenceCount++;
                    }
                }
            } catch (error) {
                // Skip invalid entries
            }
        }
        
        console.log(`Generated ${sentenceCount} sentence cards`);
    }

    async generateConjugationCards(workingDir, profile) {
        console.log('Generating conjugation cards...');
        
        // Common Catalan verbs for conjugation practice
        const commonVerbs = [
            { verb: 'ser', translations: { es: 'ser', en: 'to be', ru: 'быть' } },
            { verb: 'estar', translations: { es: 'estar', en: 'to be', ru: 'находиться' } },
            { verb: 'tenir', translations: { es: 'tener', en: 'to have', ru: 'иметь' } },
            { verb: 'fer', translations: { es: 'hacer', en: 'to do/make', ru: 'делать' } },
            { verb: 'anar', translations: { es: 'ir', en: 'to go', ru: 'идти' } },
            { verb: 'venir', translations: { es: 'venir', en: 'to come', ru: 'приходить' } },
            { verb: 'dir', translations: { es: 'decir', en: 'to say', ru: 'говорить' } },
            { verb: 'veure', translations: { es: 'ver', en: 'to see', ru: 'видеть' } }
        ];
        
        const tenses = [
            { name: 'present', label: 'Presente' },
            { name: 'preterite', label: 'Pretérito' },
            { name: 'imperfect', label: 'Imperfecto' },
            { name: 'future', label: 'Futuro' }
        ];
        
        const persons = ['jo', 'tu', 'ell/ella', 'nosaltres', 'vosaltres', 'ells/elles'];
        
        let conjugationCount = 0;
        
        for (const verbInfo of commonVerbs) {
            for (const tense of tenses) {
                for (let i = 0; i < persons.length; i++) {
                    const card = this.createConjugationCard(verbInfo, tense, persons[i], i);
                    
                    if (card) {
                        this.generatedCards.push(card);
                        this.updateDeckCounts(card.deckId);
                        conjugationCount++;
                    }
                }
            }
        }
        
        console.log(`Generated ${conjugationCount} conjugation cards`);
    }

    async generateMWECards(workingDir, profile) {
        const inputFile = path.join(workingDir, 'detected_mwes.jsonl');
        if (!fs.existsSync(inputFile)) return;
        
        console.log('Generating MWE cards...');
        
        const lines = fs.readFileSync(inputFile, 'utf8').split('\n');
        let mweCount = 0;
        
        for (const line of lines) {
            if (!line.trim()) continue;
            
            try {
                const mwe = JSON.parse(line);
                const card = this.createMWECard(mwe);
                
                if (card) {
                    this.generatedCards.push(card);
                    this.updateDeckCounts(card.deckId);
                    mweCount++;
                }
            } catch (error) {
                // Skip invalid entries
            }
        }
        
        console.log(`Generated ${mweCount} MWE cards`);
    }

    createTermCard(term) {
        return {
            id: this.generateCardId(),
            type: 'term',
            catalan: term.catalan,
            translations: term.translations,
            ipa: term.ipa || '',
            syllables: this.generateSyllables(term.catalan),
            audioIds: [],
            examples: term.examples || [],
            grammarTips: this.generateGrammarTips(term),
            tags: this.generateTags(term),
            cefr: term.cefr,
            freqRank: term.freqRank || 0,
            ease: 2.5,
            interval: 0,
            due: 0,
            stability: 4.0,
            difficulty: 4.0,
            lapses: 0,
            leechCount: 0,
            deckId: `vocab-${term.cefr.toLowerCase()}`,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            notes: ''
        };
    }

    createSentenceCard(sentence) {
        return {
            id: this.generateCardId(),
            type: 'sentence',
            catalan: sentence.catalan,
            translations: sentence.translations,
            ipa: '',
            syllables: [],
            audioIds: [],
            examples: [],
            grammarTips: [],
            tags: ['frase', sentence.cefr.toLowerCase()],
            cefr: sentence.cefr,
            freqRank: 0,
            ease: 2.5,
            interval: 0,
            due: 0,
            stability: 4.0,
            difficulty: 4.0,
            lapses: 0,
            leechCount: 0,
            deckId: `sentences-${sentence.cefr.toLowerCase()}`,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            notes: ''
        };
    }

    createClozeCard(sentence) {
        const words = sentence.catalan.split(' ');
        if (words.length < 3) return null;
        
        // Create cloze by hiding a random content word
        const contentWords = words.filter(word => word.length > 3);
        if (contentWords.length === 0) return null;
        
        const targetWord = contentWords[Math.floor(Math.random() * contentWords.length)];
        const clozeText = sentence.catalan.replace(targetWord, '___');
        
        return {
            id: this.generateCardId(),
            type: 'cloze',
            catalan: clozeText,
            translations: { answer: targetWord, ...sentence.translations },
            ipa: '',
            syllables: [],
            audioIds: [],
            examples: [sentence.catalan],
            grammarTips: [`Palabra oculta: ${targetWord}`],
            tags: ['cloze', sentence.cefr.toLowerCase()],
            cefr: sentence.cefr,
            freqRank: 0,
            ease: 2.5,
            interval: 0,
            due: 0,
            stability: 4.0,
            difficulty: 4.0,
            lapses: 0,
            leechCount: 0,
            deckId: `sentences-${sentence.cefr.toLowerCase()}`,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            notes: ''
        };
    }

    createConjugationCard(verbInfo, tense, person, personIndex) {
        // Simplified conjugation - in reality you'd use proper conjugation rules
        const conjugations = this.getConjugation(verbInfo.verb, tense.name, personIndex);
        
        return {
            id: this.generateCardId(),
            type: 'conjugation',
            catalan: `${person} ${conjugations}`,
            translations: {
                es: `${person} ${verbInfo.translations.es}`,
                en: `${person} ${verbInfo.translations.en}`,
                ru: `${person} ${verbInfo.translations.ru}`
            },
            ipa: '',
            syllables: [],
            audioIds: [],
            examples: [`${person} ${conjugations} cada dia.`],
            grammarTips: [`Verbo: ${verbInfo.verb}`, `Tiempo: ${tense.label}`, `Persona: ${person}`],
            tags: ['verbo', 'conjugación', tense.name],
            cefr: 'B1',
            freqRank: 0,
            ease: 2.5,
            interval: 0,
            due: 0,
            stability: 4.0,
            difficulty: 4.0,
            lapses: 0,
            leechCount: 0,
            deckId: 'conjugations',
            createdAt: Date.now(),
            updatedAt: Date.now(),
            notes: ''
        };
    }

    createMWECard(mwe) {
        return {
            id: this.generateCardId(),
            type: 'term',
            catalan: mwe.phrase,
            translations: this.generateMWETranslations(mwe.phrase),
            ipa: '',
            syllables: [],
            audioIds: [],
            examples: [`Exemple amb "${mwe.phrase}".`],
            grammarTips: [`Expressió de tipus: ${mwe.type}`],
            tags: ['expressió', mwe.type],
            cefr: 'B2',
            freqRank: mwe.frequency,
            ease: 2.5,
            interval: 0,
            due: 0,
            stability: 4.0,
            difficulty: 4.0,
            lapses: 0,
            leechCount: 0,
            deckId: 'expressions',
            createdAt: Date.now(),
            updatedAt: Date.now(),
            notes: ''
        };
    }

    // Helper methods
    generateCardId() {
        return `card-${Date.now()}-${this.cardIdCounter++}`;
    }

    generateSyllables(word) {
        // Simple syllable splitting
        return word.split(/[aeiouàèéíòóú]/i).filter(s => s.length > 0);
    }

    generateGrammarTips(term) {
        const tips = [];
        
        if (term.pos === 'verb') {
            tips.push('Verbo regular/irregular');
        } else if (term.pos === 'noun') {
            tips.push('Sustantivo masculino/femenino');
        } else if (term.pos === 'adjective') {
            tips.push('Adjetivo variable');
        }
        
        return tips;
    }

    generateTags(term) {
        const tags = [term.pos, term.cefr.toLowerCase()];
        
        if (term.frequency > 500) tags.push('frecuente');
        if (term.catalan.length > 8) tags.push('largo');
        
        return tags;
    }

    getConjugation(verb, tense, person) {
        // Simplified conjugation table
        const conjugations = {
            'ser': ['sóc', 'ets', 'és', 'som', 'sou', 'són'],
            'estar': ['estic', 'estàs', 'està', 'estem', 'esteu', 'estan'],
            'tenir': ['tinc', 'tens', 'té', 'tenim', 'teniu', 'tenen'],
            'fer': ['faig', 'fas', 'fa', 'fem', 'feu', 'fan'],
            'anar': ['vaig', 'vas', 'va', 'anem', 'aneu', 'van']
        };
        
        return conjugations[verb]?.[person] || `${verb}(${person})`;
    }

    generateMWETranslations(phrase) {
        // Simplified translation generation
        return {
            es: `Expresión: ${phrase}`,
            en: `Expression: ${phrase}`,
            ru: `Выражение: ${phrase}`
        };
    }

    updateDeckCounts(deckId) {
        const deck = this.deckMap.get(deckId);
        if (deck) {
            deck.counts.total++;
            deck.counts.new++;
        }
    }

    getCardTypeDistribution() {
        const distribution = {};
        
        for (const card of this.generatedCards) {
            distribution[card.type] = (distribution[card.type] || 0) + 1;
        }
        
        return distribution;
    }

    async saveCards(workingDir) {
        const outputFile = path.join(workingDir, 'generated_cards.jsonl');
        
        const content = this.generatedCards.map(card => JSON.stringify(card)).join('\n');
        fs.writeFileSync(outputFile, content, 'utf8');
        
        console.log(`Saved ${this.generatedCards.length} cards`);
    }

    async saveDecks(workingDir) {
        const outputFile = path.join(workingDir, 'generated_decks.jsonl');
        
        const decks = Array.from(this.deckMap.values());
        const content = decks.map(deck => JSON.stringify(deck)).join('\n');
        fs.writeFileSync(outputFile, content, 'utf8');
        
        console.log(`Saved ${decks.length} decks`);
    }
}

module.exports = {
    run: async (workingDir, profile) => {
        const generator = new CardGenerator();
        return generator.run(workingDir, profile);
    }
};
