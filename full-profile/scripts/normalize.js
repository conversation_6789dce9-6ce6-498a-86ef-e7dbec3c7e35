// Data normalization and cleaning script
// Cleans, deduplicates, and standardizes corpus data

const fs = require('fs');
const path = require('path');

class DataNormalizer {
    constructor() {
        this.seenTerms = new Set();
        this.seenSentences = new Set();
        this.stats = {
            termsProcessed: 0,
            termsDeduped: 0,
            sentencesProcessed: 0,
            sentencesDeduped: 0,
            invalidEntries: 0
        };
    }

    /**
     * Main normalization process
     */
    async run(workingDir, profile) {
        console.log(`🧹 Normalizing data (profile: ${profile})`);
        
        // Process terms
        await this.normalizeTerms(workingDir);
        
        // Process sentences
        await this.normalizeSentences(workingDir);
        
        // Clean frequency list
        await this.normalizeFrequencies(workingDir);
        
        return {
            profile,
            stats: this.stats
        };
    }

    /**
     * Normalize terms data
     */
    async normalizeTerms(workingDir) {
        const inputFile = path.join(workingDir, 'raw_terms.jsonl');
        const outputFile = path.join(workingDir, 'normalized_terms.jsonl');
        
        if (!fs.existsSync(inputFile)) {
            console.log('No terms file found, skipping...');
            return;
        }
        
        console.log('Normalizing terms...');
        
        const lines = fs.readFileSync(inputFile, 'utf8').split('\n');
        const normalizedTerms = [];
        
        for (const line of lines) {
            if (!line.trim()) continue;
            
            try {
                const term = JSON.parse(line);
                const normalized = this.normalizeTerm(term);
                
                if (normalized && this.isValidTerm(normalized)) {
                    const key = this.getTermKey(normalized);
                    
                    if (!this.seenTerms.has(key)) {
                        this.seenTerms.add(key);
                        normalizedTerms.push(normalized);
                    } else {
                        this.stats.termsDeduped++;
                    }
                }
                
                this.stats.termsProcessed++;
                
            } catch (error) {
                this.stats.invalidEntries++;
                console.warn(`Invalid term entry: ${line.substring(0, 50)}...`);
            }
        }
        
        // Write normalized terms
        const content = normalizedTerms.map(term => JSON.stringify(term)).join('\n');
        fs.writeFileSync(outputFile, content, 'utf8');
        
        console.log(`Normalized ${normalizedTerms.length} terms (${this.stats.termsDeduped} duplicates removed)`);
    }

    /**
     * Normalize a single term
     */
    normalizeTerm(term) {
        if (!term.catalan) return null;
        
        return {
            id: term.id || this.generateId('term'),
            catalan: this.cleanText(term.catalan),
            translations: this.normalizeTranslations(term.translations || {}),
            pos: this.normalizePos(term.pos),
            frequency: parseInt(term.frequency) || 0,
            source: term.source || 'unknown',
            ipa: this.cleanIPA(term.ipa),
            definitions: Array.isArray(term.definitions) ? term.definitions : [],
            examples: Array.isArray(term.examples) ? term.examples : []
        };
    }

    /**
     * Normalize sentences data
     */
    async normalizeSentences(workingDir) {
        const inputFiles = [
            'raw_sentences.jsonl',
            'tatoeba_sentences.jsonl',
            'opus_parallel.jsonl'
        ];
        
        const outputFile = path.join(workingDir, 'normalized_sentences.jsonl');
        const normalizedSentences = [];
        
        for (const filename of inputFiles) {
            const inputFile = path.join(workingDir, filename);
            
            if (!fs.existsSync(inputFile)) continue;
            
            console.log(`Processing ${filename}...`);
            
            const lines = fs.readFileSync(inputFile, 'utf8').split('\n');
            
            for (const line of lines) {
                if (!line.trim()) continue;
                
                try {
                    const sentence = JSON.parse(line);
                    const normalized = this.normalizeSentence(sentence);
                    
                    if (normalized && this.isValidSentence(normalized)) {
                        const key = this.getSentenceKey(normalized);
                        
                        if (!this.seenSentences.has(key)) {
                            this.seenSentences.add(key);
                            normalizedSentences.push(normalized);
                        } else {
                            this.stats.sentencesDeduped++;
                        }
                    }
                    
                    this.stats.sentencesProcessed++;
                    
                } catch (error) {
                    this.stats.invalidEntries++;
                    console.warn(`Invalid sentence entry: ${line.substring(0, 50)}...`);
                }
            }
        }
        
        // Write normalized sentences
        const content = normalizedSentences.map(sentence => JSON.stringify(sentence)).join('\n');
        fs.writeFileSync(outputFile, content, 'utf8');
        
        console.log(`Normalized ${normalizedSentences.length} sentences (${this.stats.sentencesDeduped} duplicates removed)`);
    }

    /**
     * Normalize a single sentence
     */
    normalizeSentence(sentence) {
        if (!sentence.catalan) return null;
        
        return {
            id: sentence.id || this.generateId('sentence'),
            catalan: this.cleanText(sentence.catalan),
            translations: this.normalizeTranslations(sentence.translations || {}),
            source: sentence.source || 'unknown',
            difficulty: this.normalizeDifficulty(sentence.difficulty),
            length: sentence.catalan.length,
            wordCount: this.countWords(sentence.catalan),
            linkedIds: Array.isArray(sentence.linkedIds) ? sentence.linkedIds : []
        };
    }

    /**
     * Normalize frequency list
     */
    async normalizeFrequencies(workingDir) {
        const inputFile = path.join(workingDir, 'frequency_list.txt');
        const outputFile = path.join(workingDir, 'normalized_frequencies.txt');
        
        if (!fs.existsSync(inputFile)) {
            console.log('No frequency file found, skipping...');
            return;
        }
        
        console.log('Normalizing frequency list...');
        
        const lines = fs.readFileSync(inputFile, 'utf8').split('\n');
        const frequencies = [];
        
        for (const line of lines) {
            if (!line.trim()) continue;
            
            const parts = line.split('\t');
            if (parts.length >= 2) {
                const word = this.cleanText(parts[0]);
                const freq = parseInt(parts[1]);
                
                if (word && freq > 0) {
                    frequencies.push({ word, freq });
                }
            }
        }
        
        // Sort by frequency (descending)
        frequencies.sort((a, b) => b.freq - a.freq);
        
        // Write normalized frequencies
        const content = frequencies.map(({ word, freq }) => `${word}\t${freq}`).join('\n');
        fs.writeFileSync(outputFile, content, 'utf8');
        
        console.log(`Normalized ${frequencies.length} frequency entries`);
    }

    /**
     * Text cleaning utilities
     */
    cleanText(text) {
        if (!text) return '';
        
        return text
            .trim()
            .replace(/\s+/g, ' ')  // Normalize whitespace
            .replace(/[""]/g, '"') // Normalize quotes
            .replace(/['']/g, "'") // Normalize apostrophes
            .replace(/…/g, '...')  // Normalize ellipsis
            .toLowerCase();
    }

    cleanIPA(ipa) {
        if (!ipa) return '';
        
        return ipa
            .trim()
            .replace(/^\[/, '')
            .replace(/\]$/, '')
            .replace(/\//g, '');
    }

    /**
     * Normalize translations object
     */
    normalizeTranslations(translations) {
        const normalized = {};
        const validLangs = ['es', 'en', 'ru'];
        
        for (const lang of validLangs) {
            if (translations[lang]) {
                normalized[lang] = this.cleanText(translations[lang]);
            }
        }
        
        return normalized;
    }

    /**
     * Normalize part of speech
     */
    normalizePos(pos) {
        if (!pos) return 'unknown';
        
        const posMap = {
            'n': 'noun',
            'noun': 'noun',
            'substantiu': 'noun',
            'v': 'verb',
            'verb': 'verb',
            'verbe': 'verb',
            'adj': 'adjective',
            'adjective': 'adjective',
            'adjectiu': 'adjective',
            'adv': 'adverb',
            'adverb': 'adverb',
            'adverbi': 'adverb',
            'prep': 'preposition',
            'preposition': 'preposition',
            'preposició': 'preposition',
            'conj': 'conjunction',
            'conjunction': 'conjunction',
            'conjunció': 'conjunction',
            'int': 'interjection',
            'interjection': 'interjection',
            'interjecció': 'interjection'
        };
        
        return posMap[pos.toLowerCase()] || 'unknown';
    }

    /**
     * Normalize difficulty level
     */
    normalizeDifficulty(difficulty) {
        if (!difficulty) return 'A1';
        
        const level = difficulty.toUpperCase();
        const validLevels = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'];
        
        return validLevels.includes(level) ? level : 'A1';
    }

    /**
     * Validation methods
     */
    isValidTerm(term) {
        return term.catalan && 
               term.catalan.length > 0 && 
               term.catalan.length < 100 &&
               /^[a-zA-ZàèéíòóúüçñÀÈÉÍÒÓÚÜÇÑ\s\-']+$/.test(term.catalan);
    }

    isValidSentence(sentence) {
        return sentence.catalan && 
               sentence.catalan.length > 5 && 
               sentence.catalan.length < 500 &&
               sentence.wordCount >= 2 &&
               sentence.wordCount <= 50;
    }

    /**
     * Key generation for deduplication
     */
    getTermKey(term) {
        return `${term.catalan}:${term.pos}`;
    }

    getSentenceKey(sentence) {
        return sentence.catalan;
    }

    /**
     * Utility methods
     */
    countWords(text) {
        return text.trim().split(/\s+/).length;
    }

    generateId(type) {
        return `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
}

module.exports = {
    run: async (workingDir, profile) => {
        const normalizer = new DataNormalizer();
        return normalizer.run(workingDir, profile);
    }
};
