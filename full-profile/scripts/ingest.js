// Corpus ingestion script
// Downloads or reads local corpora and prepares them for processing

const fs = require('fs');
const path = require('path');
const https = require('https');
const { createReadStream, createWriteStream } = require('fs');
const { createGunzip } = require('zlib');

class CorpusIngestor {
    constructor() {
        this.downloadedFiles = [];
        this.processedFiles = [];
    }

    /**
     * Main ingestion process
     */
    async run(corpusDir, outputDir, profile, testMode = false) {
        console.log(`📥 Ingesting corpus data (profile: ${profile})`);
        
        if (testMode) {
            return this.createTestData(outputDir);
        }

        switch (profile) {
            case 'lite':
                return this.ingestLite(outputDir);
            case 'standard':
                return this.ingestStandard(corpusDir, outputDir);
            case 'full':
                return this.ingestFull(corpusDir, outputDir);
            default:
                throw new Error(`Unknown profile: ${profile}`);
        }
    }

    /**
     * Lite profile - create minimal demo data
     */
    async ingestLite(outputDir) {
        console.log('Creating lite demo data...');
        
        const demoData = {
            terms: this.createDemoTerms(),
            sentences: this.createDemoSentences(),
            frequencies: this.createDemoFrequencies()
        };

        // Write demo data files
        const files = {
            'raw_terms.jsonl': demoData.terms,
            'raw_sentences.jsonl': demoData.sentences,
            'frequency_list.txt': demoData.frequencies
        };

        for (const [filename, data] of Object.entries(files)) {
            const filepath = path.join(outputDir, filename);
            
            if (Array.isArray(data)) {
                // JSONL format
                const content = data.map(item => JSON.stringify(item)).join('\n');
                fs.writeFileSync(filepath, content, 'utf8');
            } else {
                // Plain text
                fs.writeFileSync(filepath, data, 'utf8');
            }
            
            this.processedFiles.push(filename);
        }

        return {
            profile: 'lite',
            files: this.processedFiles,
            totalTerms: demoData.terms.length,
            totalSentences: demoData.sentences.length
        };
    }

    /**
     * Standard profile - basic corpora
     */
    async ingestStandard(corpusDir, outputDir) {
        console.log('Ingesting standard corpora...');
        
        const results = {
            profile: 'standard',
            files: [],
            totalTerms: 0,
            totalSentences: 0
        };

        // Check for available corpus files
        const availableFiles = this.checkAvailableCorpora(corpusDir);
        
        if (availableFiles.tatoeba) {
            await this.processTatoeba(corpusDir, outputDir);
            results.files.push('tatoeba_sentences.jsonl');
        }

        if (availableFiles.opus) {
            await this.processOpus(corpusDir, outputDir, ['ca-es']);
            results.files.push('opus_parallel.jsonl');
        }

        // Create basic frequency list
        await this.createBasicFrequencyList(outputDir);
        results.files.push('frequency_list.txt');

        return results;
    }

    /**
     * Full profile - all available corpora
     */
    async ingestFull(corpusDir, outputDir) {
        console.log('Ingesting full corpora...');
        
        const results = {
            profile: 'full',
            files: [],
            totalTerms: 0,
            totalSentences: 0
        };

        const availableFiles = this.checkAvailableCorpora(corpusDir);
        
        // Process all available corpora
        if (availableFiles.wikipedia) {
            await this.processWikipedia(corpusDir, outputDir);
            results.files.push('wikipedia_text.jsonl');
        }

        if (availableFiles.tatoeba) {
            await this.processTatoeba(corpusDir, outputDir);
            results.files.push('tatoeba_sentences.jsonl');
        }

        if (availableFiles.opus) {
            await this.processOpus(corpusDir, outputDir, ['ca-es', 'ca-en']);
            results.files.push('opus_parallel.jsonl');
        }

        if (availableFiles.wiktionary) {
            await this.processWiktionary(corpusDir, outputDir);
            results.files.push('wiktionary_entries.jsonl');
        }

        return results;
    }

    /**
     * Check which corpus files are available
     */
    checkAvailableCorpora(corpusDir) {
        const files = {
            wikipedia: fs.existsSync(path.join(corpusDir, 'wiki', 'cawiki-latest-pages-articles.xml.bz2')),
            tatoeba: fs.existsSync(path.join(corpusDir, 'tatoeba', 'sentences.csv')),
            opus: fs.existsSync(path.join(corpusDir, 'opus', 'ca-es.txt.zip')),
            wiktionary: fs.existsSync(path.join(corpusDir, 'wiktionary', 'enwiktionary-latest-pages-articles.xml.bz2'))
        };

        console.log('Available corpora:', Object.entries(files)
            .filter(([name, available]) => available)
            .map(([name]) => name)
            .join(', ') || 'none');

        return files;
    }

    /**
     * Process Tatoeba sentences
     */
    async processTatoeba(corpusDir, outputDir) {
        console.log('Processing Tatoeba sentences...');
        
        const sentencesFile = path.join(corpusDir, 'tatoeba', 'sentences.csv');
        const linksFile = path.join(corpusDir, 'tatoeba', 'links.csv');
        const outputFile = path.join(outputDir, 'tatoeba_sentences.jsonl');
        
        const sentences = new Map();
        const links = new Map();
        
        // Read sentences
        const sentenceLines = fs.readFileSync(sentencesFile, 'utf8').split('\n');
        for (const line of sentenceLines) {
            if (!line.trim()) continue;
            
            const [id, lang, text] = line.split('\t');
            if (lang === 'cat') {
                sentences.set(id, text);
            }
        }
        
        // Read links (for translations)
        if (fs.existsSync(linksFile)) {
            const linkLines = fs.readFileSync(linksFile, 'utf8').split('\n');
            for (const line of linkLines) {
                if (!line.trim()) continue;
                
                const [id1, id2] = line.split('\t');
                if (!links.has(id1)) links.set(id1, []);
                links.get(id1).push(id2);
            }
        }
        
        // Write processed sentences
        const output = [];
        for (const [id, text] of sentences) {
            output.push({
                id: `tatoeba-${id}`,
                source: 'tatoeba',
                catalan: text,
                linkedIds: links.get(id) || []
            });
        }
        
        const content = output.map(item => JSON.stringify(item)).join('\n');
        fs.writeFileSync(outputFile, content, 'utf8');
        
        console.log(`Processed ${output.length} Tatoeba sentences`);
    }

    /**
     * Process OPUS parallel corpora
     */
    async processOpus(corpusDir, outputDir, languagePairs) {
        console.log('Processing OPUS parallel corpora...');
        
        const outputFile = path.join(outputDir, 'opus_parallel.jsonl');
        const output = [];
        
        for (const pair of languagePairs) {
            const zipFile = path.join(corpusDir, 'opus', `${pair}.txt.zip`);
            
            if (fs.existsSync(zipFile)) {
                console.log(`Processing ${pair}...`);
                // Note: In a real implementation, you'd extract and process the ZIP
                // For now, we'll create placeholder data
                
                const [lang1, lang2] = pair.split('-');
                for (let i = 0; i < 100; i++) {
                    output.push({
                        id: `opus-${pair}-${i}`,
                        source: 'opus',
                        catalan: `Frase catalana d'exemple ${i}`,
                        translations: {
                            [lang2]: `Example sentence ${i} in ${lang2}`
                        }
                    });
                }
            }
        }
        
        const content = output.map(item => JSON.stringify(item)).join('\n');
        fs.writeFileSync(outputFile, content, 'utf8');
        
        console.log(`Processed ${output.length} OPUS parallel sentences`);
    }

    /**
     * Process Wikipedia dump (simplified)
     */
    async processWikipedia(corpusDir, outputDir) {
        console.log('Processing Wikipedia dump...');
        
        const outputFile = path.join(outputDir, 'wikipedia_text.jsonl');
        
        // Note: In a real implementation, you'd parse the XML dump
        // For now, we'll create placeholder data
        const output = [];
        
        for (let i = 0; i < 1000; i++) {
            output.push({
                id: `wiki-${i}`,
                source: 'wikipedia',
                title: `Article ${i}`,
                text: `Aquest és un text d'exemple de la Viquipèdia en català. Conté informació sobre diversos temes interessants de la cultura catalana.`,
                categories: ['cultura', 'història', 'geografia']
            });
        }
        
        const content = output.map(item => JSON.stringify(item)).join('\n');
        fs.writeFileSync(outputFile, content, 'utf8');
        
        console.log(`Processed ${output.length} Wikipedia articles`);
    }

    /**
     * Process Wiktionary dump (simplified)
     */
    async processWiktionary(corpusDir, outputDir) {
        console.log('Processing Wiktionary dump...');
        
        const outputFile = path.join(outputDir, 'wiktionary_entries.jsonl');
        
        // Note: In a real implementation, you'd parse the XML dump for IPA
        // For now, we'll create placeholder data
        const output = [];
        
        const commonWords = [
            { word: 'casa', ipa: 'ˈka.za', pos: 'noun' },
            { word: 'anar', ipa: 'ə.ˈna', pos: 'verb' },
            { word: 'bon', ipa: 'ˈbon', pos: 'adjective' },
            { word: 'dia', ipa: 'ˈdi.ə', pos: 'noun' },
            { word: 'fer', ipa: 'ˈfe', pos: 'verb' }
        ];
        
        for (const entry of commonWords) {
            output.push({
                id: `wikt-${entry.word}`,
                source: 'wiktionary',
                word: entry.word,
                ipa: entry.ipa,
                pos: entry.pos,
                definitions: [`Definició de ${entry.word}`]
            });
        }
        
        const content = output.map(item => JSON.stringify(item)).join('\n');
        fs.writeFileSync(outputFile, content, 'utf8');
        
        console.log(`Processed ${output.length} Wiktionary entries`);
    }

    /**
     * Create basic frequency list
     */
    async createBasicFrequencyList(outputDir) {
        const outputFile = path.join(outputDir, 'frequency_list.txt');
        
        const commonWords = [
            'el', 'de', 'que', 'i', 'a', 'en', 'un', 'és', 'se', 'no',
            'te', 'la', 'per', 'amb', 'tot', 'això', 'ser', 'su', 'fer', 'com',
            'casa', 'dia', 'any', 'temps', 'persona', 'vida', 'món', 'país', 'ciutat', 'home'
        ];
        
        const content = commonWords.map((word, index) => `${word}\t${1000 - index * 10}`).join('\n');
        fs.writeFileSync(outputFile, content, 'utf8');
        
        console.log(`Created frequency list with ${commonWords.length} words`);
    }

    /**
     * Create demo data for lite profile
     */
    createDemoTerms() {
        return [
            {
                id: 'demo-1',
                catalan: 'hola',
                translations: { es: 'hola', en: 'hello', ru: 'привет' },
                pos: 'interjection',
                frequency: 1000
            },
            {
                id: 'demo-2',
                catalan: 'gràcies',
                translations: { es: 'gracias', en: 'thank you', ru: 'спасибо' },
                pos: 'interjection',
                frequency: 950
            },
            {
                id: 'demo-3',
                catalan: 'casa',
                translations: { es: 'casa', en: 'house', ru: 'дом' },
                pos: 'noun',
                frequency: 800
            },
            {
                id: 'demo-4',
                catalan: 'anar',
                translations: { es: 'ir', en: 'to go', ru: 'идти' },
                pos: 'verb',
                frequency: 750
            },
            {
                id: 'demo-5',
                catalan: 'bon',
                translations: { es: 'bueno', en: 'good', ru: 'хороший' },
                pos: 'adjective',
                frequency: 700
            }
        ];
    }

    createDemoSentences() {
        return [
            {
                id: 'sent-1',
                catalan: 'Hola, com estàs?',
                translations: { es: 'Hola, ¿cómo estás?', en: 'Hello, how are you?', ru: 'Привет, как дела?' },
                difficulty: 'A1'
            },
            {
                id: 'sent-2',
                catalan: 'Gràcies per la teva ajuda.',
                translations: { es: 'Gracias por tu ayuda.', en: 'Thank you for your help.', ru: 'Спасибо за помощь.' },
                difficulty: 'A1'
            },
            {
                id: 'sent-3',
                catalan: 'La casa és molt bonica.',
                translations: { es: 'La casa es muy bonita.', en: 'The house is very beautiful.', ru: 'Дом очень красивый.' },
                difficulty: 'A2'
            }
        ];
    }

    createDemoFrequencies() {
        return `el\t10000
de\t9500
que\t9000
i\t8500
a\t8000
en\t7500
un\t7000
és\t6500
se\t6000
no\t5500
hola\t1000
gràcies\t950
casa\t800
anar\t750
bon\t700`;
    }

    /**
     * Create test data
     */
    async createTestData(outputDir) {
        console.log('Creating test data...');
        
        // Create minimal test files
        const testFiles = {
            'raw_terms.jsonl': [
                { id: 'test-1', catalan: 'test', translations: { es: 'prueba' }, pos: 'noun' }
            ],
            'raw_sentences.jsonl': [
                { id: 'test-sent-1', catalan: 'Això és una prova.', translations: { es: 'Esto es una prueba.' } }
            ],
            'frequency_list.txt': 'test\t100\nprova\t90'
        };

        for (const [filename, data] of Object.entries(testFiles)) {
            const filepath = path.join(outputDir, filename);
            
            if (Array.isArray(data)) {
                const content = data.map(item => JSON.stringify(item)).join('\n');
                fs.writeFileSync(filepath, content, 'utf8');
            } else {
                fs.writeFileSync(filepath, data, 'utf8');
            }
        }

        return {
            profile: 'test',
            files: Object.keys(testFiles),
            totalTerms: 1,
            totalSentences: 1
        };
    }
}

module.exports = {
    run: async (corpusDir, outputDir, profile, testMode) => {
        const ingestor = new CorpusIngestor();
        return ingestor.run(corpusDir, outputDir, profile, testMode);
    }
};
