// Audio and IPA attachment script
// Generates IPA pronunciations and creates audio manifest

const fs = require('fs');
const path = require('path');

class AudioIPAProcessor {
    constructor() {
        this.ipaRules = this.initializeIPARules();
        this.audioManifest = [];
        this.audioIdCounter = 1;
    }

    async run(workingDir, profile) {
        console.log(`🔊 Attaching audio and IPA (profile: ${profile})`);
        
        // Process cards to add IPA and audio references
        await this.processCards(workingDir);
        
        // Generate audio manifest
        await this.generateAudioManifest(workingDir);
        
        return {
            profile,
            cardsProcessed: this.audioIdCounter - 1,
            audioEntriesCreated: this.audioManifest.length
        };
    }

    async processCards(workingDir) {
        const inputFile = path.join(workingDir, 'generated_cards.jsonl');
        const outputFile = path.join(workingDir, 'cards_with_audio.jsonl');
        
        if (!fs.existsSync(inputFile)) return;
        
        console.log('Processing cards for IPA and audio...');
        
        const lines = fs.readFileSync(inputFile, 'utf8').split('\n');
        const processedCards = [];
        
        for (const line of lines) {
            if (!line.trim()) continue;
            
            try {
                const card = JSON.parse(line);
                const processed = this.processCard(card);
                processedCards.push(processed);
            } catch (error) {
                console.warn('Error processing card:', error.message);
            }
        }
        
        const content = processedCards.map(card => JSON.stringify(card)).join('\n');
        fs.writeFileSync(outputFile, content, 'utf8');
        
        console.log(`Processed ${processedCards.length} cards`);
    }

    processCard(card) {
        const processed = { ...card };
        
        // Generate IPA if not present
        if (!processed.ipa && processed.catalan) {
            processed.ipa = this.generateIPA(processed.catalan);
        }
        
        // Generate syllables if not present
        if (!processed.syllables || processed.syllables.length === 0) {
            processed.syllables = this.generateSyllables(processed.catalan);
        }
        
        // Create audio references
        processed.audioIds = this.createAudioReferences(processed.catalan);
        
        return processed;
    }

    generateIPA(text) {
        // Simplified IPA generation for Catalan
        // In a real implementation, this would use a proper phonetic dictionary
        
        let ipa = text.toLowerCase();
        
        // Apply basic Catalan phonetic rules
        for (const [pattern, replacement] of this.ipaRules) {
            ipa = ipa.replace(pattern, replacement);
        }
        
        return ipa;
    }

    generateSyllables(word) {
        // Simple syllable division for Catalan
        const vowels = 'aeiouàèéíòóú';
        const syllables = [];
        let currentSyllable = '';
        
        for (let i = 0; i < word.length; i++) {
            const char = word[i].toLowerCase();
            currentSyllable += char;
            
            // If we hit a vowel and the next character is a consonant (or end of word)
            if (vowels.includes(char)) {
                const nextChar = word[i + 1];
                if (!nextChar || !vowels.includes(nextChar.toLowerCase())) {
                    // Add consonant to current syllable if it exists
                    if (nextChar && i + 1 < word.length) {
                        currentSyllable += nextChar;
                        i++; // Skip the consonant we just added
                    }
                    syllables.push(currentSyllable);
                    currentSyllable = '';
                }
            }
        }
        
        // Add any remaining characters
        if (currentSyllable) {
            syllables.push(currentSyllable);
        }
        
        return syllables.length > 0 ? syllables : [word];
    }

    createAudioReferences(text) {
        const audioIds = [];
        
        // Create normal speed audio reference
        const normalId = `audio-${this.audioIdCounter++}`;
        audioIds.push(normalId);
        
        this.audioManifest.push({
            id: normalId,
            filename: `ca/${this.sanitizeFilename(text)}.mp3`,
            checksum: this.generateChecksum(text),
            duration: this.estimateDuration(text),
            kind: 'audio',
            variant: 'normal',
            text: text,
            language: 'ca'
        });
        
        // Create slow speed audio reference for longer texts
        if (text.length > 10) {
            const slowId = `audio-${this.audioIdCounter++}`;
            audioIds.push(slowId);
            
            this.audioManifest.push({
                id: slowId,
                filename: `ca/${this.sanitizeFilename(text)}_slow.mp3`,
                checksum: this.generateChecksum(text + '_slow'),
                duration: this.estimateDuration(text) * 1.5,
                kind: 'audio',
                variant: 'slow',
                text: text,
                language: 'ca'
            });
        }
        
        return audioIds;
    }

    async generateAudioManifest(workingDir) {
        const outputFile = path.join(workingDir, 'audio_manifest.jsonl');
        
        // Remove duplicates
        const uniqueManifest = this.deduplicateAudioManifest();
        
        const content = uniqueManifest.map(entry => JSON.stringify(entry)).join('\n');
        fs.writeFileSync(outputFile, content, 'utf8');
        
        console.log(`Generated audio manifest with ${uniqueManifest.length} entries`);
        
        // Also create SSML files for TTS generation
        await this.generateSSMLFiles(workingDir, uniqueManifest);
    }

    deduplicateAudioManifest() {
        const seen = new Set();
        const unique = [];
        
        for (const entry of this.audioManifest) {
            const key = `${entry.text}:${entry.variant}`;
            if (!seen.has(key)) {
                seen.add(key);
                unique.push(entry);
            }
        }
        
        return unique;
    }

    async generateSSMLFiles(workingDir, manifest) {
        const ssmlDir = path.join(workingDir, 'ssml');
        if (!fs.existsSync(ssmlDir)) {
            fs.mkdirSync(ssmlDir, { recursive: true });
        }
        
        for (const entry of manifest) {
            const ssml = this.generateSSML(entry.text, entry.variant);
            const filename = `${entry.id}.ssml`;
            const filepath = path.join(ssmlDir, filename);
            
            fs.writeFileSync(filepath, ssml, 'utf8');
        }
        
        console.log(`Generated ${manifest.length} SSML files`);
    }

    generateSSML(text, variant) {
        const rate = variant === 'slow' ? 'slow' : 'medium';
        
        return `<?xml version="1.0" encoding="UTF-8"?>
<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="ca-ES">
    <prosody rate="${rate}">
        ${this.escapeXML(text)}
    </prosody>
</speak>`;
    }

    initializeIPARules() {
        // Basic Catalan to IPA conversion rules
        return [
            // Vowels
            [/à/g, 'a'],
            [/è/g, 'ɛ'],
            [/é/g, 'e'],
            [/í/g, 'i'],
            [/ò/g, 'ɔ'],
            [/ó/g, 'o'],
            [/ú/g, 'u'],
            [/ü/g, 'y'],
            
            // Consonants
            [/ç/g, 's'],
            [/ny/g, 'ɲ'],
            [/ll/g, 'ʎ'],
            [/rr/g, 'r'],
            [/x/g, 'ʃ'],
            [/j/g, 'ʒ'],
            [/g([ei])/g, 'ʒ$1'],
            [/qu/g, 'k'],
            [/c([ei])/g, 's$1'],
            [/c([aou])/g, 'k$1'],
            
            // Final adjustments
            [/([aeiou])s$/g, '$1s'],
            [/([aeiou])n$/g, '$1n'],
            [/([aeiou])r$/g, '$1ɾ']
        ];
    }

    sanitizeFilename(text) {
        return text
            .toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '_')
            .substring(0, 50);
    }

    generateChecksum(text) {
        // Simple checksum for demo purposes
        let hash = 0;
        for (let i = 0; i < text.length; i++) {
            const char = text.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(16);
    }

    estimateDuration(text) {
        // Estimate audio duration based on text length
        // Rough estimate: 150 words per minute, average 5 characters per word
        const wordsPerMinute = 150;
        const charactersPerWord = 5;
        const words = text.length / charactersPerWord;
        const minutes = words / wordsPerMinute;
        return Math.max(minutes * 60, 1); // At least 1 second
    }

    escapeXML(text) {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&apos;');
    }
}

module.exports = {
    run: async (workingDir, profile) => {
        const processor = new AudioIPAProcessor();
        return processor.run(workingDir, profile);
    }
};
