{"name": "catalanpro-builder", "version": "1.0.0", "description": "Data pipeline for building CatalanPro learning datasets", "main": "build_decks.js", "scripts": {"build": "node build_decks.js", "build:lite": "node build_decks.js --profile=lite --output=../app/data", "build:standard": "node build_decks.js --profile=standard --output=../app/data", "build:full": "node build_decks.js --profile=full --output=../app/data", "validate": "node build_decks.js --validate --output=../app/data", "test": "node build_decks.js --profile=lite --test --output=./test-output", "clean": "rm -rf output/* ../app/data/*"}, "keywords": ["catalan", "language-learning", "spaced-repetition", "corpus-processing", "nlp"], "author": "CatalanPro Team", "license": "MIT", "engines": {"node": ">=16.0.0"}, "dependencies": {}, "devDependencies": {}, "repository": {"type": "git", "url": "https://github.com/your-username/catalanpro.git"}, "bugs": {"url": "https://github.com/your-username/catalanpro/issues"}, "homepage": "https://github.com/your-username/catalanpro#readme"}