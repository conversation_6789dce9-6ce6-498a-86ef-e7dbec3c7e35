#!/usr/bin/env node

// Main orchestrator for building Catalan learning decks
// Usage: node build_decks.js --profile=lite|standard|full --output=../app/data

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Import pipeline scripts
const ingest = require('./scripts/ingest.js');
const normalize = require('./scripts/normalize.js');
const analyzeFreqCEFR = require('./scripts/analyze_freq_cefr.js');
const detectMWE = require('./scripts/detect_mwe.js');
const generateCards = require('./scripts/generate_cards.js');
const attachAudioIPA = require('./scripts/attach_audio_ipa.js');
const validate = require('./scripts/validate.js');
const exportDecks = require('./scripts/export_decks.js');

class DeckBuilder {
    constructor() {
        this.profile = 'lite';
        this.outputDir = '../app/data';
        this.workingDir = './output';
        this.corpusDir = './corpus';
        this.verbose = false;
        this.test = false;
        this.validateOnly = false;
        
        this.stats = {
            startTime: Date.now(),
            steps: [],
            errors: [],
            warnings: []
        };
    }

    /**
     * Parse command line arguments
     */
    parseArgs() {
        const args = process.argv.slice(2);
        
        for (const arg of args) {
            if (arg.startsWith('--profile=')) {
                this.profile = arg.split('=')[1];
            } else if (arg.startsWith('--output=')) {
                this.outputDir = arg.split('=')[1];
            } else if (arg === '--verbose' || arg === '-v') {
                this.verbose = true;
            } else if (arg === '--test') {
                this.test = true;
            } else if (arg === '--validate') {
                this.validateOnly = true;
            } else if (arg === '--help' || arg === '-h') {
                this.showHelp();
                process.exit(0);
            }
        }

        // Validate profile
        if (!['lite', 'standard', 'full'].includes(this.profile)) {
            this.error(`Invalid profile: ${this.profile}. Use lite, standard, or full.`);
            process.exit(1);
        }
    }

    /**
     * Show help message
     */
    showHelp() {
        console.log(`
CatalanPro Deck Builder

Usage: node build_decks.js [options]

Options:
  --profile=PROFILE    Build profile: lite, standard, full (default: lite)
  --output=DIR         Output directory (default: ../app/data)
  --verbose, -v        Verbose output
  --test               Run with test data only
  --validate           Only validate existing output
  --help, -h           Show this help

Profiles:
  lite                 Small demo dataset (~1000 cards, no external downloads)
  standard             Medium dataset (~10k cards, basic corpora)
  full                 Complete dataset (~50k+ cards, all corpora)

Examples:
  node build_decks.js --profile=lite
  node build_decks.js --profile=full --output=../app/data --verbose
  node build_decks.js --validate
        `);
    }

    /**
     * Main build process
     */
    async build() {
        try {
            this.log(`🚀 Starting CatalanPro deck build (profile: ${this.profile})`);
            
            // Setup directories
            await this.setupDirectories();
            
            if (this.validateOnly) {
                await this.validateExistingOutput();
                return;
            }

            // Execute pipeline steps
            await this.executeStep('ingest', 'Ingesting corpus data', () => 
                ingest.run(this.corpusDir, this.workingDir, this.profile, this.test)
            );

            await this.executeStep('normalize', 'Normalizing and cleaning data', () =>
                normalize.run(this.workingDir, this.profile)
            );

            await this.executeStep('analyze', 'Analyzing frequency and CEFR levels', () =>
                analyzeFreqCEFR.run(this.workingDir, this.profile)
            );

            await this.executeStep('mwe', 'Detecting multi-word expressions', () =>
                detectMWE.run(this.workingDir, this.profile)
            );

            await this.executeStep('generate', 'Generating cards', () =>
                generateCards.run(this.workingDir, this.profile)
            );

            await this.executeStep('audio', 'Attaching audio and IPA', () =>
                attachAudioIPA.run(this.workingDir, this.profile)
            );

            await this.executeStep('validate', 'Validating generated data', () =>
                validate.run(this.workingDir, this.profile)
            );

            await this.executeStep('export', 'Exporting final decks', () =>
                exportDecks.run(this.workingDir, this.outputDir, this.profile)
            );

            // Create symlink for app
            await this.createAppLink();

            this.showSummary();

        } catch (error) {
            this.error(`Build failed: ${error.message}`);
            if (this.verbose) {
                console.error(error.stack);
            }
            process.exit(1);
        }
    }

    /**
     * Execute a pipeline step with error handling
     */
    async executeStep(name, description, fn) {
        const stepStart = Date.now();
        this.log(`\n📋 ${description}...`);
        
        try {
            const result = await fn();
            const duration = Date.now() - stepStart;
            
            this.stats.steps.push({
                name,
                description,
                duration,
                success: true,
                result
            });
            
            this.log(`✅ ${description} completed (${this.formatDuration(duration)})`);
            
            if (result && this.verbose) {
                console.log('   Result:', JSON.stringify(result, null, 2));
            }
            
        } catch (error) {
            const duration = Date.now() - stepStart;
            
            this.stats.steps.push({
                name,
                description,
                duration,
                success: false,
                error: error.message
            });
            
            this.stats.errors.push(`${name}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Setup working directories
     */
    async setupDirectories() {
        const dirs = [this.workingDir, this.outputDir];
        
        for (const dir of dirs) {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                this.log(`📁 Created directory: ${dir}`);
            }
        }

        // Create corpus directory if it doesn't exist
        if (!fs.existsSync(this.corpusDir)) {
            fs.mkdirSync(this.corpusDir, { recursive: true });
            this.log(`📁 Created corpus directory: ${this.corpusDir}`);
            this.log(`ℹ️  Place corpus files in ${this.corpusDir}/ for full profile`);
        }
    }

    /**
     * Validate existing output
     */
    async validateExistingOutput() {
        this.log('🔍 Validating existing output...');
        
        if (!fs.existsSync(this.outputDir)) {
            this.error(`Output directory does not exist: ${this.outputDir}`);
            return;
        }

        const result = await validate.validateOutput(this.outputDir);
        
        if (result.valid) {
            this.log('✅ Output validation passed');
            console.log('📊 Statistics:', JSON.stringify(result.stats, null, 2));
        } else {
            this.log('❌ Output validation failed');
            result.issues.forEach(issue => this.warn(issue));
        }
    }

    /**
     * Create symlink for app data
     */
    async createAppLink() {
        const linkDir = './app_link';
        
        try {
            // Remove existing link
            if (fs.existsSync(linkDir)) {
                fs.rmSync(linkDir, { recursive: true, force: true });
            }
            
            // Create symlink to output
            const targetPath = path.resolve(this.outputDir);
            fs.symlinkSync(targetPath, linkDir, 'dir');
            
            this.log(`🔗 Created app link: ${linkDir} -> ${targetPath}`);
            
        } catch (error) {
            this.warn(`Failed to create app link: ${error.message}`);
            
            // Fallback: copy files
            try {
                this.copyDirectory(this.outputDir, linkDir);
                this.log(`📋 Copied files to app link directory`);
            } catch (copyError) {
                this.warn(`Failed to copy files: ${copyError.message}`);
            }
        }
    }

    /**
     * Copy directory recursively
     */
    copyDirectory(src, dest) {
        if (!fs.existsSync(dest)) {
            fs.mkdirSync(dest, { recursive: true });
        }
        
        const items = fs.readdirSync(src);
        
        for (const item of items) {
            const srcPath = path.join(src, item);
            const destPath = path.join(dest, item);
            
            if (fs.statSync(srcPath).isDirectory()) {
                this.copyDirectory(srcPath, destPath);
            } else {
                fs.copyFileSync(srcPath, destPath);
            }
        }
    }

    /**
     * Show build summary
     */
    showSummary() {
        const totalDuration = Date.now() - this.stats.startTime;
        const successSteps = this.stats.steps.filter(s => s.success).length;
        const totalSteps = this.stats.steps.length;
        
        console.log(`\n🎉 Build completed successfully!`);
        console.log(`⏱️  Total time: ${this.formatDuration(totalDuration)}`);
        console.log(`✅ Steps completed: ${successSteps}/${totalSteps}`);
        
        if (this.stats.warnings.length > 0) {
            console.log(`⚠️  Warnings: ${this.stats.warnings.length}`);
        }
        
        console.log(`\n📂 Output directory: ${path.resolve(this.outputDir)}`);
        console.log(`🔗 App link: ${path.resolve('./app_link')}`);
        
        console.log(`\n🚀 Next steps:`);
        console.log(`   1. cd ../app`);
        console.log(`   2. python -m http.server 8080`);
        console.log(`   3. Open http://localhost:8080`);
        
        if (this.verbose) {
            console.log('\n📊 Step details:');
            this.stats.steps.forEach(step => {
                const status = step.success ? '✅' : '❌';
                console.log(`   ${status} ${step.name}: ${this.formatDuration(step.duration)}`);
            });
        }
    }

    /**
     * Logging methods
     */
    log(message) {
        console.log(message);
    }

    warn(message) {
        console.warn(`⚠️  ${message}`);
        this.stats.warnings.push(message);
    }

    error(message) {
        console.error(`❌ ${message}`);
        this.stats.errors.push(message);
    }

    /**
     * Format duration in human readable format
     */
    formatDuration(ms) {
        if (ms < 1000) return `${ms}ms`;
        if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
        return `${(ms / 60000).toFixed(1)}m`;
    }
}

// Main execution
async function main() {
    const builder = new DeckBuilder();
    builder.parseArgs();
    await builder.build();
}

// Run if called directly
if (require.main === module) {
    main().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}

module.exports = DeckBuilder;
