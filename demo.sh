#!/bin/bash

# CatalanPro Demo Script
# Quick setup and demo of the CatalanPro learning app

set -e

echo "🏛️ CatalanPro - Aprèn Català"
echo "=============================="
echo ""

# Check if we're in the right directory
if [ ! -f "README.md" ] || [ ! -d "app" ]; then
    echo "❌ Please run this script from the CatalanPro root directory"
    exit 1
fi

# Check for required tools
echo "🔍 Checking requirements..."

if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "❌ Python is required to run the demo server"
    echo "   Please install Python 3.x"
    exit 1
fi

if ! command -v node &> /dev/null; then
    echo "⚠️  Node.js not found - you won't be able to build custom datasets"
    echo "   But you can still run the demo with pre-built data"
fi

echo "✅ Requirements check passed"
echo ""

# Check if data exists
if [ ! -f "app/data/decks_topics.jsonl" ]; then
    echo "📦 No data found. The app includes demo data by default."
    echo "   You can build custom datasets with the full-profile build system."
fi

echo "🚀 Starting CatalanPro demo..."
echo ""
echo "📱 The app will open at: http://localhost:8080"
echo "🔧 Features to try:"
echo "   • Import wizard (first run)"
echo "   • Dashboard with study statistics"
echo "   • Review session with different modes"
echo "   • Card editor for custom content"
echo "   • Analytics and progress tracking"
echo "   • Settings and customization"
echo ""
echo "⌨️  Keyboard shortcuts during review:"
echo "   • Space: Show/hide answer"
echo "   • 1-4: Rate card (Again/Hard/Good/Easy)"
echo "   • A: Play audio"
echo "   • S: Toggle audio speed"
echo "   • Esc: End session"
echo ""
echo "📱 PWA features:"
echo "   • Install as app (browser menu)"
echo "   • Works offline after first load"
echo "   • Responsive design for mobile/desktop"
echo ""
echo "🛑 Press Ctrl+C to stop the server"
echo ""

# Change to app directory
cd app

# Start the server
if command -v python3 &> /dev/null; then
    echo "Starting server with Python 3..."
    python3 -m http.server 8080
elif command -v python &> /dev/null; then
    echo "Starting server with Python..."
    python -m http.server 8080
else
    echo "❌ No Python found"
    exit 1
fi
