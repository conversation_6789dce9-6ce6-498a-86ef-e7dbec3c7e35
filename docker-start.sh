#!/bin/bash

# CatalanPro Docker Startup Script
# Provides easy commands to run CatalanPro with Docker

set -e

echo "🏛️ CatalanPro - Docker Launcher"
echo "================================"
echo ""

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start CatalanPro in production mode (default)"
    echo "  dev       Start in development mode with hot reload"
    echo "  build     Build custom dataset using the data pipeline"
    echo "  stop      Stop all CatalanPro containers"
    echo "  logs      Show container logs"
    echo "  clean     Remove containers and images"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start    # Start production app on http://localhost:8080"
    echo "  $0 dev      # Start development server on http://localhost:8081"
    echo "  $0 build    # Build dataset and start production app"
    echo ""
}

# Function to check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        echo "❌ Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to start production app
start_production() {
    echo "🚀 Starting CatalanPro in production mode..."
    echo "📱 App will be available at: http://localhost:8080"
    echo ""
    
    docker-compose up --build -d catalanpro
    
    echo "✅ CatalanPro is starting up!"
    echo "🔍 Check status with: $0 logs"
    echo "🛑 Stop with: $0 stop"
}

# Function to start development mode
start_development() {
    echo "🔧 Starting CatalanPro in development mode..."
    echo "📱 Dev server will be available at: http://localhost:8081"
    echo "🔄 Files will be watched for changes"
    echo ""
    
    docker-compose --profile dev up --build catalanpro-dev
}

# Function to build dataset
build_dataset() {
    echo "🛠️ Building CatalanPro dataset..."
    echo ""
    
    # Run builder
    docker-compose --profile build run --rm catalanpro-builder
    
    echo ""
    echo "✅ Dataset built successfully!"
    echo "🚀 Starting production app..."
    
    # Start production app
    start_production
}

# Function to stop containers
stop_containers() {
    echo "🛑 Stopping CatalanPro containers..."
    docker-compose down
    echo "✅ Containers stopped"
}

# Function to show logs
show_logs() {
    echo "📋 CatalanPro container logs:"
    echo "=============================="
    docker-compose logs -f catalanpro
}

# Function to clean up
clean_up() {
    echo "🧹 Cleaning up CatalanPro Docker resources..."
    
    # Stop containers
    docker-compose down
    
    # Remove images
    docker-compose down --rmi all --volumes --remove-orphans
    
    # Prune unused images
    docker image prune -f
    
    echo "✅ Cleanup complete"
}

# Main script logic
check_docker

case "${1:-start}" in
    start)
        start_production
        ;;
    dev)
        start_development
        ;;
    build)
        build_dataset
        ;;
    stop)
        stop_containers
        ;;
    logs)
        show_logs
        ;;
    clean)
        clean_up
        ;;
    help|--help|-h)
        show_usage
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo ""
        show_usage
        exit 1
        ;;
esac
