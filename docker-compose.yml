version: '3.8'

services:
  catalanpro:
    build: .
    ports:
      - "8080:80"
    container_name: catalanpro-app
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    volumes:
      # Mount data directory for persistence (optional)
      - ./app/data:/usr/share/nginx/html/data:ro
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.catalanpro.rule=Host(`catalanpro.localhost`)"
      - "traefik.http.services.catalanpro.loadbalancer.server.port=80"

  # Development service with hot reload
  catalanpro-dev:
    image: node:18-alpine
    working_dir: /app
    ports:
      - "8081:8080"
    container_name: catalanpro-dev
    profiles:
      - dev
    volumes:
      - .:/app
    command: >
      sh -c "
        cd app &&
        python3 -m http.server 8080 --bind 0.0.0.0
      "
    environment:
      - NODE_ENV=development

  # Build service for generating datasets
  catalanpro-builder:
    image: node:18-alpine
    working_dir: /app
    container_name: catalanpro-builder
    profiles:
      - build
    volumes:
      - .:/app
      - ./full-profile/corpus:/app/full-profile/corpus
    command: >
      sh -c "
        cd full-profile &&
        npm install &&
        node build_decks.js --profile=lite --output=../app/data --verbose
      "
    environment:
      - NODE_ENV=development
