# CatalanPro - Dockerfile for local development and production
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy build system
COPY full-profile/ ./full-profile/
COPY package*.json ./

# Install build dependencies
RUN cd full-profile && npm install

# Build the dataset (lite profile for demo)
RUN cd full-profile && node build_decks.js --profile=lite --output=../app/data

# Production stage - lightweight web server
FROM nginx:alpine

# Copy built app
COPY app/ /usr/share/nginx/html/

# Copy built data from builder stage
COPY --from=builder /app/app/data/ /usr/share/nginx/html/data/

# Copy custom nginx configuration
COPY docker/nginx.conf /etc/nginx/conf.d/default.conf

# Create directory for logs
RUN mkdir -p /var/log/nginx

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
